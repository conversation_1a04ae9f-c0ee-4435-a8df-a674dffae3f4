# Guide d'utilisation de Laravel Permission

Ce guide explique comment utiliser le package `spatie/laravel-permission` dans votre application Laravel.

## ✅ Installation et Configuration Complète

Le package a été installé et configuré avec les éléments suivants :

### 🔧 Composants installés
- ✅ Package `spatie/laravel-permission` v6.20.0
- ✅ Migrations des tables de permissions
- ✅ Configuration des middlewares
- ✅ Modèle User configuré avec le trait HasRoles
- ✅ Seeders pour rôles, permissions et utilisateurs de test
- ✅ Commande Artisan personnalisée pour la gestion
- ✅ Directives Blade personnalisées
- ✅ Contrôleurs et vues d'exemple
- ✅ Tests complets
- ✅ Tableau de bord fonctionnel

### 1. Tables créées
- `permissions` : stocke les permissions
- `roles` : stocke les rôles
- `model_has_permissions` : relation many-to-many entre modèles et permissions
- `model_has_roles` : relation many-to-many entre modèles et rôles
- `role_has_permissions` : relation many-to-many entre rôles et permissions

### 2. Rôles et permissions par défaut
- **Rôles** : admin, user, manager
- **Permissions** : view-users, create-users, edit-users, delete-users, view-taxes, create-taxes, edit-taxes, delete-taxes, manage-settings


### Utilisation avec les middlewares
```php
// Dans routes/web.php
Route::middleware(['auth', 'permission:view-users'])->group(function () {
    Route::get('/users', [UserController::class, 'index']);
});

// Ou avec les rôles
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/admin', [AdminController::class, 'index']);
});
```

### Utilisation dans les vues Blade
```blade
@can('edit-users')
    <a href="{{ route('users.edit', $user) }}">Éditer</a>
@endcan

@role('admin')
    <a href="{{ route('admin.dashboard') }}">Administration</a>
@endrole

@hasrole('admin|manager')
    <p>Vous êtes admin ou manager</p>
@endhasrole
```

## Gestion avancée

### Créer des rôles et permissions dynamiquement
```php
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

// Créer une nouvelle permission
$permission = Permission::create(['name' => 'publish-articles']);

// Créer un nouveau rôle
$role = Role::create(['name' => 'editor']);

// Assigner la permission au rôle
$role->givePermissionTo($permission);
```

### Retirer des permissions/rôles
```php
// Retirer une permission d'un utilisateur
$user->revokePermissionTo('edit-users');

// Retirer un rôle d'un utilisateur
$user->removeRole('admin');

// Retirer toutes les permissions d'un utilisateur
$user->revokeAllPermissions();
```

### Synchroniser les permissions
```php
// Remplacer toutes les permissions actuelles par les nouvelles
$user->syncPermissions(['edit-users', 'view-users']);

// Remplacer tous les rôles actuels par les nouveaux
$user->syncRoles(['manager']);
```

## Commandes utiles

```bash
# Vider le cache des permissions
php artisan permission:cache-reset

# Créer des permissions via un seeder
php artisan db:seed --class=RoleAndPermissionSeeder

# Commande personnalisée de gestion des permissions
php artisan permission:manage list-users
php artisan permission:manage list-roles
php artisan permission:manage list-permissions
php artisan permission:manage assign-role --user=<EMAIL> --role=admin
php artisan permission:manage assign-permission --user=<EMAIL> --permission=edit-users
```

## 🎨 Directives Blade personnalisées

Le système inclut des directives Blade personnalisées pour simplifier les vérifications :

```blade
@hasPermission('edit-users')
    <button>Éditer</button>
@endhasPermission

@hasRole('admin')
    <p>Contenu admin</p>
@endhasRole

@hasAnyPermission('edit-users|delete-users')
    <p>Peut éditer OU supprimer</p>
@endhasAnyPermission

@hasAnyRole('admin|manager')
    <p>Admin ou Manager</p>
@endhasAnyRole

@isAdmin
    <p>Utilisateur administrateur</p>
@endisAdmin

@canManageUsers
    <p>Peut gérer les utilisateurs</p>
@endcanManageUsers
```

## 🏠 Tableau de bord

Un tableau de bord complet est disponible à `/dashboard` qui affiche :
- Statistiques du système
- Rôles et permissions de l'utilisateur
- Actions disponibles selon les permissions
- Informations système

## 👥 Utilisateurs de test

Les utilisateurs suivants sont créés automatiquement :
- **Admin** : <EMAIL> / password123 (tous droits)
- **Manager** : <EMAIL> / password123 (gestion limitée)
- **User** : <EMAIL> / password123 (consultation uniquement)

## Bonnes pratiques

1. **Utilisez des noms descriptifs** pour les permissions (ex: 'edit-users' plutôt que 'edit')
2. **Groupez les permissions logiquement** (ex: toutes les permissions liées aux utilisateurs commencent par 'user-')
3. **Utilisez les rôles pour grouper les permissions** plutôt que d'assigner chaque permission individuellement
4. **Testez vos permissions** avec des tests automatisés
5. **Documentez vos permissions** pour que l'équipe comprenne leur utilisation

## Dépannage

Si vous rencontrez des problèmes :

1. Vérifiez que le trait `HasRoles` est bien ajouté à votre modèle User
2. Assurez-vous que les migrations ont été exécutées
3. Videz le cache des permissions avec `php artisan permission:cache-reset`
4. Vérifiez la configuration dans `config/permission.php`
