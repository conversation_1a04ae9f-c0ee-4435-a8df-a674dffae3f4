<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Détails de l'utilisateur</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .card { border: 1px solid #ddd; border-radius: 5px; padding: 20px; margin-bottom: 20px; }
        .btn { padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-right: 10px; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .badge { background-color: #e9ecef; padding: 4px 8px; border-radius: 3px; font-size: 12px; margin-right: 5px; }
        .badge-role { background-color: #007bff; color: white; }
        .badge-permission { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>Détails de l'utilisateur</h1>

    <div class="card">
        <h2>Informations personnelles</h2>
        <p><strong>ID :</strong> {{ $user->id }}</p>
        <p><strong>Nom :</strong> {{ $user->name }}</p>
        <p><strong>Email :</strong> {{ $user->email }}</p>
        <p><strong>Créé le :</strong> {{ $user->created_at->format('d/m/Y H:i') }}</p>
        <p><strong>Dernière mise à jour :</strong> {{ $user->updated_at->format('d/m/Y H:i') }}</p>
    </div>

    <div class="card">
        <h2>Rôles</h2>
        @if($user->roles->count() > 0)
            @foreach($user->roles as $role)
                <span class="badge badge-role">{{ $role->name }}</span>
            @endforeach
        @else
            <p>Aucun rôle assigné</p>
        @endif
    </div>

    <div class="card">
        <h2>Permissions directes</h2>
        @if($user->permissions->count() > 0)
            @foreach($user->permissions as $permission)
                <span class="badge badge-permission">{{ $permission->name }}</span>
            @endforeach
        @else
            <p>Aucune permission directe assignée</p>
        @endif
    </div>

    <div class="card">
        <h2>Toutes les permissions (via rôles et directes)</h2>
        @php
            $allPermissions = $user->getAllPermissions();
        @endphp
        @if($allPermissions->count() > 0)
            @foreach($allPermissions as $permission)
                <span class="badge badge-permission">{{ $permission->name }}</span>
            @endforeach
        @else
            <p>Aucune permission</p>
        @endif
    </div>

    <div style="margin-top: 20px;">
        @can('edit-users')
            <a href="{{ route('users.edit', $user) }}" class="btn btn-primary">Éditer</a>
        @endcan
        
        <a href="{{ route('users.index') }}" class="btn btn-secondary">Retour à la liste</a>
    </div>
</body>
</html>
