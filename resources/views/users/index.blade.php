<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des utilisateurs</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 5px 10px; text-decoration: none; border-radius: 3px; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>Liste des utilisateurs</h1>
    
    @if(session('success'))
        <div style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin-bottom: 20px;">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-bottom: 20px;">
            {{ session('error') }}
        </div>
    @endif

    @hasPermission('create-users')
        <a href="{{ route('users.create') }}" class="btn btn-primary" style="margin-bottom: 20px; display: inline-block;">
            Créer un utilisateur
        </a>
    @endhasPermission

    @isAdmin
        <div style="background-color: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin-bottom: 20px;">
            <strong>Mode Administrateur :</strong> Vous avez accès à toutes les fonctionnalités.
        </div>
    @endisAdmin

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Nom</th>
                <th>Email</th>
                <th>Rôles</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($users as $user)
                <tr>
                    <td>{{ $user->id }}</td>
                    <td>{{ $user->name }}</td>
                    <td>{{ $user->email }}</td>
                    <td>
                        @foreach($user->roles as $role)
                            <span style="background-color: #e9ecef; padding: 2px 6px; border-radius: 3px; font-size: 12px;">
                                {{ $role->name }}
                            </span>
                        @endforeach
                    </td>
                    <td>
                        @hasPermission('view-users')
                            <a href="{{ route('users.show', $user) }}" class="btn btn-primary">Voir</a>
                        @endhasPermission

                        @hasAnyPermission('edit-users|delete-users')
                            @hasPermission('edit-users')
                                <a href="{{ route('users.edit', $user) }}" class="btn btn-primary">Éditer</a>
                            @endhasPermission

                            @hasPermission('delete-users')
                                @if($user->id !== auth()->id())
                                    <form style="display: inline;" method="POST" action="{{ route('users.destroy', $user) }}"
                                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger">Supprimer</button>
                                    </form>
                                @endif
                            @endhasPermission
                        @endhasAnyPermission

                        @hasRole('admin')
                            <span style="color: #dc3545; font-size: 12px;">👑 Admin</span>
                        @endhasRole
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="5" style="text-align: center;">Aucun utilisateur trouvé</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    @if(method_exists($users, 'links'))
        <div style="margin-top: 20px;">
            {{ $users->links() }}
        </div>
    @endif
</body>
</html>
