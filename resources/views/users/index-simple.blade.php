<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des utilisateurs</title>
</head>
<body>
    <h1>Liste des utilisateurs</h1>
    
    @if(session('success'))
        <div>{{ session('success') }}</div>
    @endif

    @if(session('error'))
        <div>{{ session('error') }}</div>
    @endif

    @can('create-users')
        <a href="{{ route('users.create') }}">Créer un utilisateur</a>
    @endcan

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Nom</th>
                <th>Email</th>
                <th>Rôles</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($users as $user)
                <tr>
                    <td>{{ $user->id }}</td>
                    <td>{{ $user->name }}</td>
                    <td>{{ $user->email }}</td>
                    <td>
                        @foreach($user->roles as $role)
                            <span>{{ $role->name }}</span>
                        @endforeach
                    </td>
                    <td>
                        @can('view-users')
                            <a href="{{ route('users.show', $user) }}">Voir</a>
                        @endcan
                        
                        @can('edit-users')
                            <a href="{{ route('users.edit', $user) }}">Éditer</a>
                        @endcan
                        
                        @can('delete-users')
                            @if($user->id !== auth()->id())
                                <form method="POST" action="{{ route('users.destroy', $user) }}">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit">Supprimer</button>
                                </form>
                            @endif
                        @endcan
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="5">Aucun utilisateur trouvé</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    @if(method_exists($users, 'links'))
        {{ $users->links() }}
    @endif
</body>
</html>
