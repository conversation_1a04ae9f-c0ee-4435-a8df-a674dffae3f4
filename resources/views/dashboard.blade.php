<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - Taxe App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #6c757d; margin-top: 5px; }
        .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin: 2px; }
        .badge-role { background-color: #007bff; color: white; }
        .badge-permission { background-color: #28a745; color: white; }
        .badge-action { background-color: #17a2b8; color: white; }
        .btn { padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Tableau de bord - Taxe App</h1>
            <p>Bienvenue, <strong>{{ auth()->user()->name }}</strong> !</p>
            
            @isAdmin
                <div class="alert alert-warning">
                    <strong>👑 Mode Administrateur</strong> - Vous avez accès à toutes les fonctionnalités du système.
                </div>
            @endisAdmin
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ $stats['total_users'] }}</div>
                <div class="stat-label">Utilisateurs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['total_roles'] }}</div>
                <div class="stat-label">Rôles</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['total_permissions'] }}</div>
                <div class="stat-label">Permissions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['user_roles'] }}</div>
                <div class="stat-label">Mes Rôles</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['user_permissions'] }}</div>
                <div class="stat-label">Mes Permissions</div>
            </div>
        </div>

        <div class="grid-2">
            <!-- Mes rôles et permissions -->
            <div class="section">
                <h3>🎭 Mes Rôles</h3>
                @if(count($userRoles) > 0)
                    @foreach($userRoles as $role)
                        <span class="badge badge-role">{{ $role }}</span>
                    @endforeach
                @else
                    <p>Aucun rôle assigné</p>
                @endif

                <h3 style="margin-top: 20px;">🔐 Mes Permissions</h3>
                @if(count($userPermissions) > 0)
                    @foreach($userPermissions as $permission)
                        <span class="badge badge-permission">{{ $permission }}</span>
                    @endforeach
                @else
                    <p>Aucune permission</p>
                @endif
            </div>

            <!-- Actions disponibles -->
            <div class="section">
                <h3>⚡ Actions Disponibles</h3>
                
                @if($availableActions['can_view_users'])
                    <a href="{{ route('users.index') }}" class="btn btn-primary">👥 Voir les utilisateurs</a>
                @endif

                @if($availableActions['can_manage_users'])
                    <span class="badge badge-action">Gestion des utilisateurs</span>
                @endif

                @if($availableActions['can_view_taxes'])
                    <span class="badge badge-action">Consultation des taxes</span>
                @endif

                @if($availableActions['can_manage_taxes'])
                    <span class="badge badge-action">Gestion des taxes</span>
                @endif

                @if($availableActions['can_manage_settings'])
                    <span class="badge badge-action">Gestion des paramètres</span>
                @endif

                @if(!$availableActions['can_view_users'] && !$availableActions['can_manage_users'] && !$availableActions['can_view_taxes'])
                    <div class="alert alert-info">
                        Aucune action spéciale disponible avec vos permissions actuelles.
                    </div>
                @endif
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="section">
            <h3>🚀 Actions Rapides</h3>
            
            @canManageUsers
                <a href="{{ route('users.index') }}" class="btn btn-primary">Gérer les utilisateurs</a>
            @endcanManageUsers

            @hasPermission('create-users')
                <a href="{{ route('users.create') }}" class="btn btn-success">Créer un utilisateur</a>
            @endhasPermission

            @hasAnyRole('admin|manager')
                <button class="btn btn-warning" onclick="alert('Fonctionnalité de gestion avancée')">Gestion avancée</button>
            @endhasAnyRole

            @hasRole('admin')
                <button class="btn btn-danger" onclick="alert('Panneau d\'administration')">Administration</button>
            @endhasRole
        </div>

        <!-- Informations système -->
        <div class="section">
            <h3>ℹ️ Informations Système</h3>
            <p><strong>Votre email :</strong> {{ auth()->user()->email }}</p>
            <p><strong>Membre depuis :</strong> {{ auth()->user()->created_at->format('d/m/Y') }}</p>
            <p><strong>Dernière connexion :</strong> {{ now()->format('d/m/Y H:i') }}</p>
            
            @hasAnyPermission('view-users|edit-users|delete-users|create-users')
                <div class="alert alert-success">
                    ✅ Vous avez des permissions de gestion des utilisateurs
                </div>
            @endhasAnyPermission
        </div>
    </div>
</body>
</html>
