<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ManagePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:manage
                            {action : Action à effectuer (list-users, list-roles, list-permissions, assign-role, assign-permission)}
                            {--user= : Email de l\'utilisateur}
                            {--role= : Nom du rôle}
                            {--permission= : Nom de la permission}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gérer les rôles et permissions des utilisateurs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list-users':
                $this->listUsers();
                break;
            case 'list-roles':
                $this->listRoles();
                break;
            case 'list-permissions':
                $this->listPermissions();
                break;
            case 'assign-role':
                $this->assignRole();
                break;
            case 'assign-permission':
                $this->assignPermission();
                break;
            default:
                $this->error('Action non reconnue. Actions disponibles : list-users, list-roles, list-permissions, assign-role, assign-permission');
        }
    }

    private function listUsers()
    {
        $users = User::with('roles', 'permissions')->get();

        $this->info('Liste des utilisateurs :');
        $this->table(
            ['ID', 'Nom', 'Email', 'Rôles', 'Permissions directes'],
            $users->map(function ($user) {
                return [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->roles->pluck('name')->join(', '),
                    $user->permissions->pluck('name')->join(', ')
                ];
            })
        );
    }

    private function listRoles()
    {
        $roles = Role::with('permissions')->get();

        $this->info('Liste des rôles :');
        $this->table(
            ['Nom', 'Permissions'],
            $roles->map(function ($role) {
                return [
                    $role->name,
                    $role->permissions->pluck('name')->join(', ')
                ];
            })
        );
    }

    private function listPermissions()
    {
        $permissions = Permission::all();

        $this->info('Liste des permissions :');
        $this->table(
            ['Nom'],
            $permissions->map(function ($permission) {
                return [$permission->name];
            })
        );
    }

    private function assignRole()
    {
        $userEmail = $this->option('user');
        $roleName = $this->option('role');

        if (!$userEmail || !$roleName) {
            $this->error('Vous devez spécifier --user et --role');
            return;
        }

        $user = User::where('email', $userEmail)->first();
        if (!$user) {
            $this->error("Utilisateur avec l'email {$userEmail} non trouvé");
            return;
        }

        $role = Role::where('name', $roleName)->first();
        if (!$role) {
            $this->error("Rôle {$roleName} non trouvé");
            return;
        }

        $user->assignRole($role);
        $this->info("Rôle {$roleName} assigné à {$user->name} ({$userEmail})");
    }

    private function assignPermission()
    {
        $userEmail = $this->option('user');
        $permissionName = $this->option('permission');

        if (!$userEmail || !$permissionName) {
            $this->error('Vous devez spécifier --user et --permission');
            return;
        }

        $user = User::where('email', $userEmail)->first();
        if (!$user) {
            $this->error("Utilisateur avec l'email {$userEmail} non trouvé");
            return;
        }

        $permission = Permission::where('name', $permissionName)->first();
        if (!$permission) {
            $this->error("Permission {$permissionName} non trouvée");
            return;
        }

        $user->givePermissionTo($permission);
        $this->info("Permission {$permissionName} assignée à {$user->name} ({$userEmail})");
    }
}
