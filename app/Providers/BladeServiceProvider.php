<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class BladeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Directive pour vérifier si l'utilisateur a une permission spécifique
        Blade::if('hasPermission', function ($permission) {
            return auth()->check() && auth()->user()->hasPermissionTo($permission);
        });

        // Directive pour vérifier si l'utilisateur a un rôle spécifique
        Blade::if('hasRole', function ($role) {
            return auth()->check() && auth()->user()->hasRole($role);
        });

        // Directive pour vérifier si l'utilisateur a l'une des permissions
        Blade::if('hasAnyPermission', function ($permissions) {
            if (!auth()->check()) {
                return false;
            }

            if (is_string($permissions)) {
                $permissions = explode('|', $permissions);
            }

            return auth()->user()->hasAnyPermission($permissions);
        });

        // Directive pour vérifier si l'utilisateur a l'un des rôles
        Blade::if('hasAnyRole', function ($roles) {
            if (!auth()->check()) {
                return false;
            }

            if (is_string($roles)) {
                $roles = explode('|', $roles);
            }

            return auth()->user()->hasAnyRole($roles);
        });

        // Directive pour vérifier si l'utilisateur est admin
        Blade::if('isAdmin', function () {
            return auth()->check() && auth()->user()->hasRole('admin');
        });

        // Directive pour vérifier si l'utilisateur peut gérer d'autres utilisateurs
        Blade::if('canManageUsers', function () {
            return auth()->check() && auth()->user()->hasAnyPermission(['create-users', 'edit-users', 'delete-users']);
        });
    }
}
