<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $permission
     * @param  string|null  $guard
     */
    public function handle(Request $request, Closure $next, string $permission, string $guard = null): Response
    {
        if (!auth($guard)->check()) {
            abort(401, 'Non authentifié');
        }

        if (!auth($guard)->user()->hasPermissionTo($permission)) {
            abort(403, "Vous n'avez pas la permission '{$permission}' requise pour accéder à cette ressource.");
        }

        return $next($request);
    }
}
