<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $user = auth()->user();

        // Statistiques générales
        $stats = [
            'total_users' => User::count(),
            'total_roles' => Role::count(),
            'total_permissions' => Permission::count(),
            'user_roles' => $user->roles->count(),
            'user_permissions' => $user->getAllPermissions()->count(),
        ];

        // Permissions de l'utilisateur actuel
        $userPermissions = $user->getAllPermissions()->pluck('name')->toArray();
        $userRoles = $user->roles->pluck('name')->toArray();

        // Actions disponibles selon les permissions
        $availableActions = [
            'can_manage_users' => $user->hasAnyPermission(['create-users', 'edit-users', 'delete-users']),
            'can_view_users' => $user->hasPermissionTo('view-users'),
            'can_manage_taxes' => $user->hasAnyPermission(['create-taxes', 'edit-taxes', 'delete-taxes']),
            'can_view_taxes' => $user->hasPermissionTo('view-taxes'),
            'can_manage_settings' => $user->hasPermissionTo('manage-settings'),
            'is_admin' => $user->hasRole('admin'),
        ];

        return view('dashboard', compact('stats', 'userPermissions', 'userRoles', 'availableActions'));
    }
}
