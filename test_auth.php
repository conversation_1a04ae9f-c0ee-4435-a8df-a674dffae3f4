<?php

/**
 * Script de test pour vérifier le système d'authentification
 * 
 * Ce script teste les fonctionnalités suivantes :
 * - Connexion avec les utilisateurs de test
 * - Vérification des rôles et permissions
 * - Accès aux pages protégées
 */

echo "=== Test du système d'authentification ===\n\n";

echo "Utilisateurs de test créés :\n";
echo "1. Admin: <EMAIL> / password123\n";
echo "   - Accès complet à toutes les fonctionnalités\n";
echo "   - Peut gérer les utilisateurs, rôles et permissions\n\n";

echo "2. Manager: <EMAIL> / password123\n";
echo "   - Peut voir, créer et éditer les utilisateurs\n";
echo "   - Ne peut pas supprimer les utilisateurs\n\n";

echo "3. User: <EMAIL> / password123\n";
echo "   - Peut seulement voir les utilisateurs\n";
echo "   - Accès limité aux fonctionnalités\n\n";

echo "Pages à tester :\n";
echo "- Page d'accueil : http://127.0.0.1:8001/\n";
echo "- Login : http://127.0.0.1:8001/login\n";
echo "- Register : http://127.0.0.1:8001/register\n";
echo "- Dashboard : http://127.0.0.1:8001/dashboard (nécessite une connexion)\n";
echo "- Utilisateurs : http://127.0.0.1:8001/users (nécessite une connexion)\n";
echo "- Profil : http://127.0.0.1:8001/profile (nécessite une connexion)\n\n";

echo "Instructions de test :\n";
echo "1. Ouvrez http://127.0.0.1:8001/ dans votre navigateur\n";
echo "2. Cliquez sur 'Log in' ou 'Register' pour tester l'authentification\n";
echo "3. Connectez-vous avec un des comptes de test\n";
echo "4. Vérifiez que la navigation inclut les liens Dashboard et Utilisateurs\n";
echo "5. Testez l'accès aux différentes pages selon les permissions\n";
echo "6. Testez la déconnexion\n\n";

echo "=== Fin du script de test ===\n";
