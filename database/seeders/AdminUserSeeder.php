<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur admin par défaut
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrateur',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        // Assigner le rôle admin
        $admin->assignRole('admin');

        // Créer un utilisateur manager de test
        $manager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Manager Test',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        // Assigner le rôle manager
        $manager->assignRole('manager');

        // Créer un utilisateur normal de test
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Utilisateur Test',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        // Assigner le rôle user
        $user->assignRole('user');

        $this->command->info('Utilisateurs de test créés :');
        $this->command->info('Admin: <EMAIL> / password123');
        $this->command->info('Manager: <EMAIL> / password123');
        $this->command->info('User: <EMAIL> / password123');
    }
}
