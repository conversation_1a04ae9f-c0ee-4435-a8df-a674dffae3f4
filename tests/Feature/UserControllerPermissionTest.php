<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class UserControllerPermissionTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_with_view_permission_can_access_users_index(): void
    {
        // Créer un utilisateur avec la permission view-users
        $user = User::factory()->create();
        $permission = Permission::create(['name' => 'view-users']);
        $user->givePermissionTo($permission);

        // Se connecter et tester l'accès
        $response = $this->actingAs($user)->get('/users');

        // Devrait réussir (pas de 403)
        $response->assertStatus(200);
    }

    public function test_user_without_view_permission_cannot_access_users_index(): void
    {
        // Créer un utilisateur sans permission
        $user = User::factory()->create();

        // Se connecter et tester l'accès
        $response = $this->actingAs($user)->get('/users');

        // Devrait échouer avec 403 Forbidden
        $response->assertStatus(403);
    }
}
