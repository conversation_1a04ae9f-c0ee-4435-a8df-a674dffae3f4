<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class PermissionTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_be_assigned_role(): void
    {
        // Créer un utilisateur
        $user = User::factory()->create();

        // Créer un rôle
        $role = Role::create(['name' => 'admin']);

        // Assigner le rôle à l'utilisateur
        $user->assignRole($role);

        // Vérifier que l'utilisateur a le rôle
        $this->assertTrue($user->hasRole('admin'));
    }

    public function test_user_can_be_assigned_permission(): void
    {
        // Créer un utilisateur
        $user = User::factory()->create();

        // Créer une permission
        $permission = Permission::create(['name' => 'view-users']);

        // Assigner la permission à l'utilisateur
        $user->givePermissionTo($permission);

        // Vérifier que l'utilisateur a la permission
        $this->assertTrue($user->hasPermissionTo('view-users'));
    }

    public function test_role_can_have_permissions(): void
    {
        // Créer un rôle
        $role = Role::create(['name' => 'manager']);

        // Créer des permissions
        $permission1 = Permission::create(['name' => 'view-users']);
        $permission2 = Permission::create(['name' => 'edit-users']);

        // Assigner les permissions au rôle
        $role->givePermissionTo([$permission1, $permission2]);

        // Vérifier que le rôle a les permissions
        $this->assertTrue($role->hasPermissionTo('view-users'));
        $this->assertTrue($role->hasPermissionTo('edit-users'));
    }

    public function test_user_inherits_permissions_from_role(): void
    {
        // Créer un utilisateur
        $user = User::factory()->create();

        // Créer un rôle avec des permissions
        $role = Role::create(['name' => 'editor']);
        $permission = Permission::create(['name' => 'edit-content']);
        $role->givePermissionTo($permission);

        // Assigner le rôle à l'utilisateur
        $user->assignRole($role);

        // Vérifier que l'utilisateur hérite de la permission du rôle
        $this->assertTrue($user->hasPermissionTo('edit-content'));
    }
}
