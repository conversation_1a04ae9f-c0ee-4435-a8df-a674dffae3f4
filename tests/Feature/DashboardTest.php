<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class DashboardTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer les permissions et rôles de base
        Permission::create(['name' => 'view-users']);
        Permission::create(['name' => 'create-users']);
        Permission::create(['name' => 'edit-users']);
        Permission::create(['name' => 'delete-users']);
        Permission::create(['name' => 'view-taxes']);
        Permission::create(['name' => 'manage-settings']);

        $adminRole = Role::create(['name' => 'admin']);
        $managerRole = Role::create(['name' => 'manager']);
        $userRole = Role::create(['name' => 'user']);

        $adminRole->givePermissionTo(['view-users', 'create-users', 'edit-users', 'delete-users', 'view-taxes', 'manage-settings']);
        $managerRole->givePermissionTo(['view-users', 'edit-users', 'view-taxes']);
        $userRole->givePermissionTo(['view-taxes']);
    }

    public function test_authenticated_user_can_access_dashboard(): void
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Tableau de bord');
        $response->assertSee($user->name);
    }

    public function test_dashboard_shows_correct_stats_for_admin(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        // Créer quelques utilisateurs supplémentaires
        User::factory()->count(3)->create();

        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('4'); // Total users (admin + 3 autres)
        $response->assertSee('Mode Administrateur');
    }

    public function test_dashboard_shows_user_roles_and_permissions(): void
    {
        $manager = User::factory()->create();
        $manager->assignRole('manager');

        $response = $this->actingAs($manager)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('manager'); // Le rôle
        $response->assertSee('view-users'); // Une permission du rôle
        $response->assertSee('edit-users'); // Une autre permission
    }

    public function test_dashboard_shows_available_actions_based_on_permissions(): void
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        // L'utilisateur avec le rôle 'user' ne devrait pas voir les actions de gestion
        $response->assertDontSee('Gérer les utilisateurs');
        $response->assertDontSee('Créer un utilisateur');
    }

    public function test_dashboard_shows_management_actions_for_admin(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Gérer les utilisateurs');
        $response->assertSee('Créer un utilisateur');
        $response->assertSee('Administration');
    }

    public function test_unauthenticated_user_cannot_access_dashboard(): void
    {
        $response = $this->get('/dashboard');

        // Devrait être redirigé vers la page de connexion ou recevoir une erreur 401/403
        $this->assertNotEquals(200, $response->getStatusCode());
    }

    public function test_dashboard_displays_user_information(): void
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);
        $user->assignRole('manager');

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Test User');
        $response->assertSee('<EMAIL>');
        $response->assertSee('manager');
    }
}
