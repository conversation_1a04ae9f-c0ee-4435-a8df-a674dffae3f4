<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ComplexPermissionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer les permissions de base
        Permission::create(['name' => 'view-users']);
        Permission::create(['name' => 'edit-users']);
        Permission::create(['name' => 'delete-users']);

        // Créer les rôles
        $adminRole = Role::create(['name' => 'admin']);
        $managerRole = Role::create(['name' => 'manager']);
        $userRole = Role::create(['name' => 'user']);

        // Assigner les permissions aux rôles
        $adminRole->givePermissionTo(['view-users', 'edit-users', 'delete-users']);
        $managerRole->givePermissionTo(['view-users', 'edit-users']);
        $userRole->givePermissionTo(['view-users']);
    }

    public function test_admin_can_access_all_user_actions(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        // Test accès à la liste
        $response = $this->actingAs($admin)->get('/users');
        $response->assertStatus(200);

        // Test accès aux détails
        $targetUser = User::factory()->create();
        $response = $this->actingAs($admin)->get("/users/{$targetUser->id}");
        $response->assertStatus(200);
    }

    public function test_manager_can_view_and_edit_but_not_delete(): void
    {
        $manager = User::factory()->create();
        $manager->assignRole('manager');
        $targetUser = User::factory()->create();

        // Peut voir la liste
        $response = $this->actingAs($manager)->get('/users');
        $response->assertStatus(200);

        // Peut voir les détails
        $response = $this->actingAs($manager)->get("/users/{$targetUser->id}");
        $response->assertStatus(200);

        // Ne peut pas supprimer (pas de permission delete-users)
        $this->assertFalse($manager->hasPermissionTo('delete-users'));
    }

    public function test_user_can_only_view(): void
    {
        $user = User::factory()->create();
        $user->assignRole('user');

        // Peut voir la liste
        $response = $this->actingAs($user)->get('/users');
        $response->assertStatus(200);

        // Peut voir les détails
        $targetUser = User::factory()->create();
        $response = $this->actingAs($user)->get("/users/{$targetUser->id}");
        $response->assertStatus(200);

        // N'a pas les permissions d'édition ou suppression
        $this->assertFalse($user->hasPermissionTo('edit-users'));
        $this->assertFalse($user->hasPermissionTo('delete-users'));
    }

    public function test_user_with_direct_permission_overrides_role(): void
    {
        $user = User::factory()->create();
        $user->assignRole('user'); // Rôle qui n'a que view-users

        // Donner une permission directe
        $user->givePermissionTo('edit-users');

        // Maintenant l'utilisateur a les deux permissions
        $this->assertTrue($user->hasPermissionTo('view-users')); // Via le rôle
        $this->assertTrue($user->hasPermissionTo('edit-users')); // Permission directe
        $this->assertFalse($user->hasPermissionTo('delete-users')); // Ni via rôle ni directe
    }

    public function test_user_with_multiple_roles(): void
    {
        $user = User::factory()->create();

        // Assigner plusieurs rôles
        $user->assignRole(['user', 'manager']);

        // L'utilisateur hérite de toutes les permissions des deux rôles
        $this->assertTrue($user->hasPermissionTo('view-users'));
        $this->assertTrue($user->hasPermissionTo('edit-users'));
        $this->assertFalse($user->hasPermissionTo('delete-users')); // Seul admin a cette permission
    }

    public function test_permission_caching_works(): void
    {
        $user = User::factory()->create();
        $user->assignRole('admin');

        // Première vérification (met en cache)
        $this->assertTrue($user->hasPermissionTo('view-users'));

        // Retirer la permission du rôle
        $adminRole = Role::findByName('admin');
        $adminRole->revokePermissionTo('view-users');

        // Vider le cache des permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Maintenant l'utilisateur ne devrait plus avoir la permission
        $this->assertFalse($user->fresh()->hasPermissionTo('view-users'));
    }

    public function test_unauthorized_user_cannot_access_protected_routes(): void
    {
        // Utilisateur connecté mais sans permission
        $user = User::factory()->create();
        // Ne pas assigner de rôle ou permission

        $response = $this->actingAs($user)->get('/users');
        $response->assertStatus(403); // Forbidden
    }
}
