{"__meta": {"id": "01JY6B9JJXYQHDSYA4GTJCVVFW", "datetime": "2025-06-20 09:39:07", "utime": **********.999674, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.4.5", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.322814, "end": **********.999692, "duration": 0.6768779754638672, "duration_str": "677ms", "measures": [{"label": "Booting", "start": **********.322814, "relative_start": 0, "end": **********.536033, "relative_end": **********.536033, "duration": 0.*****************, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.536051, "relative_start": 0.*****************, "end": **********.999694, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "464ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.609133, "relative_start": 0.****************, "end": **********.619619, "relative_end": **********.619619, "duration": 0.*****************, "duration_str": "10.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7954992, "peak_usage_str": "8MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Validation\\ValidationException", "message": "These credentials do not match our records.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Validation/ValidationException.php", "line": 71, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:53</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"39 characters\">app/Http/Requests/Auth/LoginRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">withMessages</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Validation\\ValidationException</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"43 characters\">These credentials do not match our records.</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">app/Http/Controllers/Auth/AuthenticatedSessionController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>28</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">App\\Http\\Requests\\Auth\\LoginRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"56 characters\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">[object App\\Http\\Requests\\Auth\\LoginRequest]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Http\\Requests\\Auth\\LoginRequest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Requests\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LoginRequest</span></span> {<a class=sf-dump-ref>#1479</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ParameterBag</span></span> {<a class=sf-dump-ref>#1481</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">InputBag</span></span> {<a class=sf-dump-ref>#1478</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n              \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n              \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">InputBag</span></span> {<a class=sf-dump-ref>#1480</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ServerBag</span></span> {<a class=sf-dump-ref>#1484</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/public</span>\"\n              \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50667</span>\"\n              \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"30 characters\">PHP/8.4.5 (Development Server)</span>\"\n              \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n              \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n              \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n              \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n              \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n              \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n              \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"72 characters\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/public/index.php</span>\"\n              \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n              \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n              \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n              \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n              \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n              \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n              \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n              \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n              \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8001/login</span>\"\n              \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n              \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n              \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n              \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1001 characters\">XSRF-TOKEN=eyJpdiI6Ikx1QlMxT2xsMGdkZUdOaS9taHdJamc9PSIsInZhbHVlIjoiNHc4TTVIL1RVWkFaaUo5TXhkc2lmWnBrRGZxSlIxVC9nQ2tsbklOTnNyWWFnK1ZheGk3ZmZqQmJlTVV3M2doSFEzUjRncWNuY0tzY3h1TnVkQ3FOQWs0V3dWd21XUU9FeDJXVEMzN1Qwck9Cd3lnNEpwcXpZblgwSXA0WTBLaG4iLCJtYWMiOiJjYmJlYjQ1OWU3YjljNDIyM2NhYmMzMmZiMDI3YzFmOWVlOWYwNDYxNWY0MzgyNjdlOTQ4YTg2NmE0YWFhNTA5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRXYW5XRU92eWU2Y1FFd2ZFWkdUZ0E9PSIsInZhbHVlIjoiM0JHSU1md3RaS0ZhSkJlTkRKK3hhM1VVa1dmSHBMMG5mci9lYkNnTEpybzlRNHpFbGdkSXBObFY3R01VNUhRbnl1WVRPUWNOd1QzOGcxa2lYVExOdnZtK0tlV3ZndE1jbUt1bEpLaUFlVlJzZER2VVZmRnovdDB1andnK2ZnUWIiLCJtYWMiOiJmYzAwMjA3ZjRhOGFmYTkxODdmZGRkNjZjYTg0N2NkNmY5NWNjYWM5MDc2ZDAzYmY5NDkxMGI5YjQ1NmM4NDE5IiwidGFnIjoiIn0%3D; _ga_Z28525QR15=GS2.1.s1749595141$o5$g0$t1749595141$j60$l0$h0; _ga=GA1.1.E20B8A103D1D49E6A741E19936EFAC67; localauth=localapi9d9bde7eb655a1ef:; _ga_CJB9T8MR54=GS2.1.s1749564387$o1$g1$t1749564392$j55$l0$h0; _ga_WEZVWJKCXR=GS2.1.s1749564388$o1$g0$t1749564388$j60$l0$h0; isNotIncognito=true</span>\"\n              \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.3228</span>\n              \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FileBag</span></span> {<a class=sf-dump-ref>#1483</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">InputBag</span></span> {<a class=sf-dump-ref>#1482</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n              \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MPFbKSj1cMFroXoAqBuGEKE7S5ajt4nbmlFoNSOe</span>\"\n              \"<span class=sf-dump-key>_ga_Z28525QR15</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>localauth</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>_ga_CJB9T8MR54</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>_ga_WEZVWJKCXR</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>isNotIncognito</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HeaderBag</span></span> {<a class=sf-dump-ref>#1485</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8001/login</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1001 characters\">XSRF-TOKEN=eyJpdiI6Ikx1QlMxT2xsMGdkZUdOaS9taHdJamc9PSIsInZhbHVlIjoiNHc4TTVIL1RVWkFaaUo5TXhkc2lmWnBrRGZxSlIxVC9nQ2tsbklOTnNyWWFnK1ZheGk3ZmZqQmJlTVV3M2doSFEzUjRncWNuY0tzY3h1TnVkQ3FOQWs0V3dWd21XUU9FeDJXVEMzN1Qwck9Cd3lnNEpwcXpZblgwSXA0WTBLaG4iLCJtYWMiOiJjYmJlYjQ1OWU3YjljNDIyM2NhYmMzMmZiMDI3YzFmOWVlOWYwNDYxNWY0MzgyNjdlOTQ4YTg2NmE0YWFhNTA5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRXYW5XRU92eWU2Y1FFd2ZFWkdUZ0E9PSIsInZhbHVlIjoiM0JHSU1md3RaS0ZhSkJlTkRKK3hhM1VVa1dmSHBMMG5mci9lYkNnTEpybzlRNHpFbGdkSXBObFY3R01VNUhRbnl1WVRPUWNOd1QzOGcxa2lYVExOdnZtK0tlV3ZndE1jbUt1bEpLaUFlVlJzZER2VVZmRnovdDB1andnK2ZnUWIiLCJtYWMiOiJmYzAwMjA3ZjRhOGFmYTkxODdmZGRkNjZjYTg0N2NkNmY5NWNjYWM5MDc2ZDAzYmY5NDkxMGI5YjQ1NmM4NDE5IiwidGFnIjoiIn0%3D; _ga_Z28525QR15=GS2.1.s1749595141$o5$g0$t1749595141$j60$l0$h0; _ga=GA1.1.E20B8A103D1D49E6A741E19936EFAC67; localauth=localapi9d9bde7eb655a1ef:; _ga_CJB9T8MR54=GS2.1.s1749564387$o1$g1$t1749564392$j55$l0$h0; _ga_WEZVWJKCXR=GS2.1.s1749564388$o1$g0$t1749564388$j60$l0$h0; isNotIncognito=true</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">content</span>: \"<span class=sf-dump-str title=\"88 characters\">_token=k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX&amp;email=test%40gmail.com&amp;password=password</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">method</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Session\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21452 title=\"3 occurrences\">#1452</a><samp data-depth=6 id=sf-dump-**********-ref21452 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">id</span>: \"<span class=sf-dump-str title=\"40 characters\">MPFbKSj1cMFroXoAqBuGEKE7S5ajt4nbmlFoNSOe</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"15 characters\">laravel_session</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n              \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n              \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8001/register</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>old</span>\" => []\n                \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">_old_input</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">errors</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>_old_input</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n              </samp>]\n              \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref>#1504</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">bags</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#1540</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">These credentials do not match our records.</span>\"\n                      </samp>]\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n                  </samp>}\n                </samp>]\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">handler</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Session\\FileSessionHandler\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FileSessionHandler</span></span> {<a class=sf-dump-ref>#1451</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">files</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Filesystem\\Filesystem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Filesystem</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Filesystem</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2174 title=\"2 occurrences\">#174</a>}\n              #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"82 characters\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/storage/framework/sessions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">minutes</span>: \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">serialization</span>: \"<span class=sf-dump-str title=\"3 characters\">php</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">started</span>: <span class=sf-dump-const>true</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">locale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isSafeContentPreferred</span>: <span class=sf-dump-const title=\"Uninitialized property\">? bool</span>\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">trustedValuesCache</span>: []\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isIisRewrite</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">InputBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21486 title=\"2 occurrences\">#1486</a><samp data-depth=6 id=sf-dump-**********-ref21486 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21405 title=\"2 occurrences\">#1405</a><samp data-depth=6 id=sf-dump-**********-ref21405 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">AuthServiceProvider</span></span>\"\n            <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AuthServiceProvider</span></span> {<a class=sf-dump-ref>#158</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$app</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php\n124 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span><span class=\"sf-dump-ellipsis-tail\">src/Illuminate/Auth/AuthServiceProvider.php</span></span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21422 title=\"2 occurrences\">#1422</a><samp data-depth=6 id=sf-dump-**********-ref21422 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span>\"\n            <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n            <span class=sf-dump-meta>use</span>: {<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>$route</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21296 title=\"4 occurrences\">#1296</a> &#8230;}\n            </samp>}\n            <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Routing/Router.php\n114 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span><span class=\"sf-dump-ellipsis-tail\">src/Illuminate/Routing/Router.php</span></span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">779 to 779</span>\"\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n          #<span class=sf-dump-protected title=\"Protected property\">redirector</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Redirector\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Redirector</span></span> {<a class=sf-dump-ref>#1477</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">generator</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\UrlGenerator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">UrlGenerator</span></span> {<a class=sf-dump-ref>#1476</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">routes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RouteCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">RouteCollection</span></span> {<a class=sf-dump-ref>#31</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">routes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>GET</span>\" => <span class=sf-dump-note>array:21</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>_debugbar/open</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2176 title=\"5 occurrences\">#176</a><samp data-depth=11 id=sf-dump-**********-ref2176 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"14 characters\">_debugbar/open</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>domain</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@handle</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"20 characters\">debugbar.openhandler</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@handle</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Barryvdh\\Debugbar\\Controllers</span>\"\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_debugbar</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>_debugbar/clockwork/{id}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2177 title=\"5 occurrences\">#177</a><samp data-depth=11 id=sf-dump-**********-ref2177 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"24 characters\">_debugbar/clockwork/{id}</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>domain</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@clockwork</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"18 characters\">debugbar.clockwork</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@clockwork</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Barryvdh\\Debugbar\\Controllers</span>\"\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_debugbar</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>_debugbar/assets/stylesheets</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2178 title=\"5 occurrences\">#178</a><samp data-depth=11 id=sf-dump-**********-ref2178 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"28 characters\">_debugbar/assets/stylesheets</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>domain</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Barryvdh\\Debugbar\\Controllers\\AssetController@css</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"19 characters\">debugbar.assets.css</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Barryvdh\\Debugbar\\Controllers\\AssetController@css</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Barryvdh\\Debugbar\\Controllers</span>\"\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_debugbar</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>_debugbar/assets/javascript</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2179 title=\"5 occurrences\">#179</a><samp data-depth=11 id=sf-dump-**********-ref2179 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"27 characters\">_debugbar/assets/javascript</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>domain</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Barryvdh\\Debugbar\\Controllers\\AssetController@js</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"18 characters\">debugbar.assets.js</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Barryvdh\\Debugbar\\Controllers\\AssetController@js</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Barryvdh\\Debugbar\\Controllers</span>\"\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_debugbar</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>sanctum/csrf-cookie</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2183 title=\"5 occurrences\">#183</a><samp data-depth=11 id=sf-dump-**********-ref2183 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"19 characters\">sanctum/csrf-cookie</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"7 characters\">sanctum</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"19 characters\">sanctum.csrf-cookie</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>_ignition/health-check</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2196 title=\"5 occurrences\">#196</a><samp data-depth=11 id=sf-dump-**********-ref2196 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"22 characters\">_ignition/health-check</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController@__invoke</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"20 characters\">ignition.healthCheck</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_ignition</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>api/user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2318 title=\"3 occurrences\">#318</a><samp data-depth=11 id=sf-dump-**********-ref2318 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"8 characters\">api/user</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">api</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">auth:sanctum</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => <span class=sf-dump-note>Closure(Request $request)</span> {<a class=sf-dump-ref>#317</a><samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RouteFileRegistrar\n37 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">RouteFileRegistrar</span></span>\"\n                          <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RouteFileRegistrar\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">RouteFileRegistrar</span></span> {<a class=sf-dump-ref>#315</a> &#8230;}\n                          <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/routes/api.php\n70 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/</span><span class=\"sf-dump-ellipsis-tail\">routes/api.php</span></span>\"\n                          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">17 to 19</span>\"\n                        </samp>}\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"3 characters\">api</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>/</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2320 title=\"3 occurrences\">#320</a><samp data-depth=11 id=sf-dump-**********-ref2320 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str>/</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#319</a><samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RouteFileRegistrar\n37 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">RouteFileRegistrar</span></span>\"\n                          <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RouteFileRegistrar\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">RouteFileRegistrar</span></span> {<a class=sf-dump-ref>#316</a> &#8230;}\n                          <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/routes/web.php\n70 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/</span><span class=\"sf-dump-ellipsis-tail\">routes/web.php</span></span>\"\n                          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">17 to 19</span>\"\n                        </samp>}\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>dashboard</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2321 title=\"5 occurrences\">#321</a><samp data-depth=11 id=sf-dump-**********-ref2321 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"9 characters\">dashboard</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">verified</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Controllers\\DashboardController@index</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Controllers\\DashboardController@index</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"9 characters\">dashboard</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>profile</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2324 title=\"5 occurrences\">#324</a><samp data-depth=11 id=sf-dump-**********-ref2324 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"7 characters\">profile</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"43 characters\">App\\Http\\Controllers\\ProfileController@edit</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"43 characters\">App\\Http\\Controllers\\ProfileController@edit</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"12 characters\">profile.edit</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>users</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21149 title=\"5 occurrences\">#1149</a><samp data-depth=11 id=sf-dump-**********-ref21149 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"11 characters\">users.index</span>\"\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"41 characters\">App\\Http\\Controllers\\UserController@index</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"41 characters\">App\\Http\\Controllers\\UserController@index</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>users/create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21079 title=\"5 occurrences\">#1079</a><samp data-depth=11 id=sf-dump-**********-ref21079 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"12 characters\">users/create</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"12 characters\">users.create</span>\"\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"42 characters\">App\\Http\\Controllers\\UserController@create</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"42 characters\">App\\Http\\Controllers\\UserController@create</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>users/{user}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21080 title=\"5 occurrences\">#1080</a><samp data-depth=11 id=sf-dump-**********-ref21080 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"12 characters\">users/{user}</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"10 characters\">users.show</span>\"\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"40 characters\">App\\Http\\Controllers\\UserController@show</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"40 characters\">App\\Http\\Controllers\\UserController@show</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>users/{user}/edit</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2671 title=\"5 occurrences\">#671</a><samp data-depth=11 id=sf-dump-**********-ref2671 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"17 characters\">users/{user}/edit</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"10 characters\">users.edit</span>\"\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"40 characters\">App\\Http\\Controllers\\UserController@edit</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"40 characters\">App\\Http\\Controllers\\UserController@edit</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>register</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2328 title=\"5 occurrences\">#328</a><samp data-depth=11 id=sf-dump-**********-ref2328 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"8 characters\">register</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">guest</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"57 characters\">App\\Http\\Controllers\\Auth\\RegisteredUserController@create</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"57 characters\">App\\Http\\Controllers\\Auth\\RegisteredUserController@create</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"8 characters\">register</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>login</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2329 title=\"5 occurrences\">#329</a><samp data-depth=11 id=sf-dump-**********-ref2329 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"5 characters\">login</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">guest</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"63 characters\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"63 characters\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"5 characters\">login</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>forgot-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21297 title=\"5 occurrences\">#1297</a><samp data-depth=11 id=sf-dump-**********-ref21297 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"15 characters\">forgot-password</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">guest</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"60 characters\">App\\Http\\Controllers\\Auth\\PasswordResetLinkController@create</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"60 characters\">App\\Http\\Controllers\\Auth\\PasswordResetLinkController@create</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"16 characters\">password.request</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>reset-password/{token}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21299 title=\"5 occurrences\">#1299</a><samp data-depth=11 id=sf-dump-**********-ref21299 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"22 characters\">reset-password/{token}</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">guest</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"54 characters\">App\\Http\\Controllers\\Auth\\NewPasswordController@create</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"54 characters\">App\\Http\\Controllers\\Auth\\NewPasswordController@create</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"14 characters\">password.reset</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>verify-email</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21301 title=\"5 occurrences\">#1301</a><samp data-depth=11 id=sf-dump-**********-ref21301 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"12 characters\">verify-email</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"68 characters\">App\\Http\\Controllers\\Auth\\EmailVerificationPromptController@__invoke</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"59 characters\">App\\Http\\Controllers\\Auth\\EmailVerificationPromptController</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"19 characters\">verification.notice</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>verify-email/{id}/{hash}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21302 title=\"5 occurrences\">#1302</a><samp data-depth=11 id=sf-dump-**********-ref21302 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"24 characters\">verify-email/{id}/{hash}</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">signed</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">throttle:6,1</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"56 characters\">App\\Http\\Controllers\\Auth\\VerifyEmailController@__invoke</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"47 characters\">App\\Http\\Controllers\\Auth\\VerifyEmailController</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"19 characters\">verification.verify</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>confirm-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21304 title=\"5 occurrences\">#1304</a><samp data-depth=11 id=sf-dump-**********-ref21304 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"16 characters\">confirm-password</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"60 characters\">App\\Http\\Controllers\\Auth\\ConfirmablePasswordController@show</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"60 characters\">App\\Http\\Controllers\\Auth\\ConfirmablePasswordController@show</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"16 characters\">password.confirm</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                  </samp>]\n                  \"<span class=sf-dump-key>HEAD</span>\" => <span class=sf-dump-note>array:21</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>_debugbar/open</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2176 title=\"5 occurrences\">#176</a>}\n                    \"<span class=sf-dump-key>_debugbar/clockwork/{id}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2177 title=\"5 occurrences\">#177</a>}\n                    \"<span class=sf-dump-key>_debugbar/assets/stylesheets</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2178 title=\"5 occurrences\">#178</a>}\n                    \"<span class=sf-dump-key>_debugbar/assets/javascript</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2179 title=\"5 occurrences\">#179</a>}\n                    \"<span class=sf-dump-key>sanctum/csrf-cookie</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2183 title=\"5 occurrences\">#183</a>}\n                    \"<span class=sf-dump-key>_ignition/health-check</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2196 title=\"5 occurrences\">#196</a>}\n                    \"<span class=sf-dump-key>api/user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2318 title=\"3 occurrences\">#318</a>}\n                    \"<span class=sf-dump-key>/</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2320 title=\"3 occurrences\">#320</a>}\n                    \"<span class=sf-dump-key>dashboard</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2321 title=\"5 occurrences\">#321</a>}\n                    \"<span class=sf-dump-key>profile</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2324 title=\"5 occurrences\">#324</a>}\n                    \"<span class=sf-dump-key>users</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21149 title=\"5 occurrences\">#1149</a>}\n                    \"<span class=sf-dump-key>users/create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21079 title=\"5 occurrences\">#1079</a>}\n                    \"<span class=sf-dump-key>users/{user}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21080 title=\"5 occurrences\">#1080</a>}\n                    \"<span class=sf-dump-key>users/{user}/edit</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2671 title=\"5 occurrences\">#671</a>}\n                    \"<span class=sf-dump-key>register</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2328 title=\"5 occurrences\">#328</a>}\n                    \"<span class=sf-dump-key>login</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2329 title=\"5 occurrences\">#329</a>}\n                    \"<span class=sf-dump-key>forgot-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21297 title=\"5 occurrences\">#1297</a>}\n                    \"<span class=sf-dump-key>reset-password/{token}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21299 title=\"5 occurrences\">#1299</a>}\n                    \"<span class=sf-dump-key>verify-email</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21301 title=\"5 occurrences\">#1301</a>}\n                    \"<span class=sf-dump-key>verify-email/{id}/{hash}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21302 title=\"5 occurrences\">#1302</a>}\n                    \"<span class=sf-dump-key>confirm-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21304 title=\"5 occurrences\">#1304</a>}\n                  </samp>]\n                  \"<span class=sf-dump-key>DELETE</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>_debugbar/cache/{key}/{tags?}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2180 title=\"4 occurrences\">#180</a><samp data-depth=11 id=sf-dump-**********-ref2180 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"29 characters\">_debugbar/cache/{key}/{tags?}</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">DELETE</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>domain</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Barryvdh\\Debugbar\\Controllers\\CacheController@delete</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"21 characters\">debugbar.cache.delete</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Barryvdh\\Debugbar\\Controllers\\CacheController@delete</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Barryvdh\\Debugbar\\Controllers</span>\"\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_debugbar</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>profile</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2326 title=\"4 occurrences\">#326</a><samp data-depth=11 id=sf-dump-**********-ref2326 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"7 characters\">profile</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">DELETE</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Controllers\\ProfileController@destroy</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Controllers\\ProfileController@destroy</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"15 characters\">profile.destroy</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>users/{user}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21295 title=\"4 occurrences\">#1295</a><samp data-depth=11 id=sf-dump-**********-ref21295 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"12 characters\">users/{user}</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">DELETE</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"13 characters\">users.destroy</span>\"\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"43 characters\">App\\Http\\Controllers\\UserController@destroy</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"43 characters\">App\\Http\\Controllers\\UserController@destroy</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                  </samp>]\n                  \"<span class=sf-dump-key>POST</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>_debugbar/queries/explain</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2181 title=\"4 occurrences\">#181</a><samp data-depth=11 id=sf-dump-**********-ref2181 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"25 characters\">_debugbar/queries/explain</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>domain</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Barryvdh\\Debugbar\\Controllers\\QueriesController@explain</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"24 characters\">debugbar.queries.explain</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Barryvdh\\Debugbar\\Controllers\\QueriesController@explain</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Barryvdh\\Debugbar\\Controllers</span>\"\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_debugbar</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\Routing\\CompiledRoute\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CompiledRoute</span></span> {<a class=sf-dump-ref>#1425</a><samp data-depth=12 class=sf-dump-compact>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">variables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">tokens</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=14 class=sf-dump-compact>\n                            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"26 characters\">/_debugbar/queries/explain</span>\"\n                          </samp>]\n                        </samp>]\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">staticPrefix</span>: \"<span class=sf-dump-str title=\"26 characters\">/_debugbar/queries/explain</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">regex</span>: \"<span class=sf-dump-str title=\"33 characters\">{^/_debugbar/queries/explain$}sDu</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">pathVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostRegex</span>: <span class=sf-dump-const>null</span>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostTokens</span>: []\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>_ignition/execute-solution</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2197 title=\"4 occurrences\">#197</a><samp data-depth=11 id=sf-dump-**********-ref2197 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"26 characters\">_ignition/execute-solution</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"74 characters\">Spatie\\LaravelIgnition\\Http\\Controllers\\ExecuteSolutionController@__invoke</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Spatie\\LaravelIgnition\\Http\\Controllers\\ExecuteSolutionController</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"24 characters\">ignition.executeSolution</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_ignition</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\Routing\\CompiledRoute\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CompiledRoute</span></span> {<a class=sf-dump-ref>#1430</a><samp data-depth=12 class=sf-dump-compact>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">variables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">tokens</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=14 class=sf-dump-compact>\n                            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"27 characters\">/_ignition/execute-solution</span>\"\n                          </samp>]\n                        </samp>]\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">staticPrefix</span>: \"<span class=sf-dump-str title=\"27 characters\">/_ignition/execute-solution</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">regex</span>: \"<span class=sf-dump-str title=\"35 characters\">{^/_ignition/execute\\-solution$}sDu</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">pathVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostRegex</span>: <span class=sf-dump-const>null</span>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostTokens</span>: []\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>_ignition/update-config</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2198 title=\"4 occurrences\">#198</a><samp data-depth=11 id=sf-dump-**********-ref2198 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"23 characters\">_ignition/update-config</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"71 characters\">Spatie\\LaravelIgnition\\Http\\Controllers\\UpdateConfigController@__invoke</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Spatie\\LaravelIgnition\\Http\\Controllers\\UpdateConfigController</span>\"\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"21 characters\">ignition.updateConfig</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"<span class=sf-dump-str title=\"9 characters\">_ignition</span>\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\Routing\\CompiledRoute\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CompiledRoute</span></span> {<a class=sf-dump-ref>#1431</a><samp data-depth=12 class=sf-dump-compact>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">variables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">tokens</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=14 class=sf-dump-compact>\n                            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"24 characters\">/_ignition/update-config</span>\"\n                          </samp>]\n                        </samp>]\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">staticPrefix</span>: \"<span class=sf-dump-str title=\"24 characters\">/_ignition/update-config</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">regex</span>: \"<span class=sf-dump-str title=\"32 characters\">{^/_ignition/update\\-config$}sDu</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">pathVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostRegex</span>: <span class=sf-dump-const>null</span>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostTokens</span>: []\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>users</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21081 title=\"4 occurrences\">#1081</a><samp data-depth=11 id=sf-dump-**********-ref21081 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"11 characters\">users.store</span>\"\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"41 characters\">App\\Http\\Controllers\\UserController@store</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"41 characters\">App\\Http\\Controllers\\UserController@store</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\Routing\\CompiledRoute\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CompiledRoute</span></span> {<a class=sf-dump-ref>#1432</a><samp data-depth=12 class=sf-dump-compact>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">variables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">tokens</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=14 class=sf-dump-compact>\n                            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">/users</span>\"\n                          </samp>]\n                        </samp>]\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">staticPrefix</span>: \"<span class=sf-dump-str title=\"6 characters\">/users</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">regex</span>: \"<span class=sf-dump-str title=\"13 characters\">{^/users$}sDu</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">pathVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostRegex</span>: <span class=sf-dump-const>null</span>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostTokens</span>: []\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>register</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2327 title=\"3 occurrences\">#327</a><samp data-depth=11 id=sf-dump-**********-ref2327 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"8 characters\">register</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">guest</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"56 characters\">App\\Http\\Controllers\\Auth\\RegisteredUserController@store</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"56 characters\">App\\Http\\Controllers\\Auth\\RegisteredUserController@store</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\Routing\\CompiledRoute\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CompiledRoute</span></span> {<a class=sf-dump-ref>#1433</a><samp data-depth=12 class=sf-dump-compact>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">variables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">tokens</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=14 class=sf-dump-compact>\n                            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">/register</span>\"\n                          </samp>]\n                        </samp>]\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">staticPrefix</span>: \"<span class=sf-dump-str title=\"9 characters\">/register</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">regex</span>: \"<span class=sf-dump-str title=\"16 characters\">{^/register$}sDu</span>\"\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">pathVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostVariables</span>: []\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostRegex</span>: <span class=sf-dump-const>null</span>\n                        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostTokens</span>: []\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>login</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21296 title=\"4 occurrences\">#1296</a> &#8230;}\n                    \"<span class=sf-dump-key>forgot-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21298 title=\"4 occurrences\">#1298</a><samp data-depth=11 id=sf-dump-**********-ref21298 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"15 characters\">forgot-password</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">guest</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"59 characters\">App\\Http\\Controllers\\Auth\\PasswordResetLinkController@store</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"59 characters\">App\\Http\\Controllers\\Auth\\PasswordResetLinkController@store</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"14 characters\">password.email</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>reset-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21300 title=\"4 occurrences\">#1300</a><samp data-depth=11 id=sf-dump-**********-ref21300 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"14 characters\">reset-password</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">guest</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"53 characters\">App\\Http\\Controllers\\Auth\\NewPasswordController@store</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"53 characters\">App\\Http\\Controllers\\Auth\\NewPasswordController@store</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"14 characters\">password.store</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>email/verification-notification</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21303 title=\"4 occurrences\">#1303</a><samp data-depth=11 id=sf-dump-**********-ref21303 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"31 characters\">email/verification-notification</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">throttle:6,1</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"71 characters\">App\\Http\\Controllers\\Auth\\EmailVerificationNotificationController@store</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"71 characters\">App\\Http\\Controllers\\Auth\\EmailVerificationNotificationController@store</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"17 characters\">verification.send</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>confirm-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21305 title=\"3 occurrences\">#1305</a><samp data-depth=11 id=sf-dump-**********-ref21305 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"16 characters\">confirm-password</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"61 characters\">App\\Http\\Controllers\\Auth\\ConfirmablePasswordController@store</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"61 characters\">App\\Http\\Controllers\\Auth\\ConfirmablePasswordController@store</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>logout</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21307 title=\"4 occurrences\">#1307</a><samp data-depth=11 id=sf-dump-**********-ref21307 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"6 characters\">logout</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"64 characters\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@destroy</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"64 characters\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@destroy</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"6 characters\">logout</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                  </samp>]\n                  \"<span class=sf-dump-key>PATCH</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>profile</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2325 title=\"4 occurrences\">#325</a><samp data-depth=11 id=sf-dump-**********-ref2325 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"7 characters\">profile</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">PATCH</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"45 characters\">App\\Http\\Controllers\\ProfileController@update</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"45 characters\">App\\Http\\Controllers\\ProfileController@update</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"14 characters\">profile.update</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                    \"<span class=sf-dump-key>users/{user}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21030 title=\"5 occurrences\">#1030</a><samp data-depth=11 id=sf-dump-**********-ref21030 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"12 characters\">users/{user}</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n                        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">PATCH</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"12 characters\">users.update</span>\"\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"42 characters\">App\\Http\\Controllers\\UserController@update</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"42 characters\">App\\Http\\Controllers\\UserController@update</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                  </samp>]\n                  \"<span class=sf-dump-key>PUT</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>users/{user}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21030 title=\"5 occurrences\">#1030</a>}\n                    \"<span class=sf-dump-key>password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21306 title=\"4 occurrences\">#1306</a><samp data-depth=11 id=sf-dump-**********-ref21306 class=sf-dump-compact>\n                      +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=12 class=sf-dump-compact>\n                        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                        </samp>]\n                        \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"51 characters\">App\\Http\\Controllers\\Auth\\PasswordController@update</span>\"\n                        \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"51 characters\">App\\Http\\Controllers\\Auth\\PasswordController@update</span>\"\n                        \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                        \"<span class=sf-dump-key>where</span>\" => []\n                        \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"15 characters\">password.update</span>\"\n                      </samp>]\n                      +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n                      +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">parameters</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref227 title=\"38 occurrences\">#27</a> &#8230;}\n                      #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                      #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n                    </samp>}\n                  </samp>]\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">allRoutes</span>: <span class=sf-dump-note>array:38</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>HEAD_debugbar/open</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2176 title=\"5 occurrences\">#176</a>}\n                  \"<span class=sf-dump-key>HEAD_debugbar/clockwork/{id}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2177 title=\"5 occurrences\">#177</a>}\n                  \"<span class=sf-dump-key>HEAD_debugbar/assets/stylesheets</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2178 title=\"5 occurrences\">#178</a>}\n                  \"<span class=sf-dump-key>HEAD_debugbar/assets/javascript</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2179 title=\"5 occurrences\">#179</a>}\n                  \"<span class=sf-dump-key>DELETE_debugbar/cache/{key}/{tags?}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2180 title=\"4 occurrences\">#180</a>}\n                  \"<span class=sf-dump-key>POST_debugbar/queries/explain</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2181 title=\"4 occurrences\">#181</a>}\n                  \"<span class=sf-dump-key>HEADsanctum/csrf-cookie</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2183 title=\"5 occurrences\">#183</a>}\n                  \"<span class=sf-dump-key>HEAD_ignition/health-check</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2196 title=\"5 occurrences\">#196</a>}\n                  \"<span class=sf-dump-key>POST_ignition/execute-solution</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2197 title=\"4 occurrences\">#197</a>}\n                  \"<span class=sf-dump-key>POST_ignition/update-config</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2198 title=\"4 occurrences\">#198</a>}\n                  \"<span class=sf-dump-key>HEADapi/user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2318 title=\"3 occurrences\">#318</a>}\n                  \"<span class=sf-dump-key>HEAD/</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2320 title=\"3 occurrences\">#320</a>}\n                  \"<span class=sf-dump-key>HEADdashboard</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2321 title=\"5 occurrences\">#321</a>}\n                  \"<span class=sf-dump-key>HEADprofile</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2324 title=\"5 occurrences\">#324</a>}\n                  \"<span class=sf-dump-key>PATCHprofile</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2325 title=\"4 occurrences\">#325</a>}\n                  \"<span class=sf-dump-key>DELETEprofile</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2326 title=\"4 occurrences\">#326</a>}\n                  \"<span class=sf-dump-key>HEADusers</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21149 title=\"5 occurrences\">#1149</a>}\n                  \"<span class=sf-dump-key>HEADusers/create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21079 title=\"5 occurrences\">#1079</a>}\n                  \"<span class=sf-dump-key>POSTusers</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21081 title=\"4 occurrences\">#1081</a>}\n                  \"<span class=sf-dump-key>HEADusers/{user}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21080 title=\"5 occurrences\">#1080</a>}\n                  \"<span class=sf-dump-key>HEADusers/{user}/edit</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2671 title=\"5 occurrences\">#671</a>}\n                  \"<span class=sf-dump-key>PATCHusers/{user}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21030 title=\"5 occurrences\">#1030</a>}\n                  \"<span class=sf-dump-key>DELETEusers/{user}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21295 title=\"4 occurrences\">#1295</a>}\n                  \"<span class=sf-dump-key>HEADregister</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2328 title=\"5 occurrences\">#328</a>}\n                  \"<span class=sf-dump-key>POSTregister</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2327 title=\"3 occurrences\">#327</a>}\n                  \"<span class=sf-dump-key>HEADlogin</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2329 title=\"5 occurrences\">#329</a>}\n                  \"<span class=sf-dump-key>POSTlogin</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21296 title=\"4 occurrences\">#1296</a> &#8230;}\n                  \"<span class=sf-dump-key>HEADforgot-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21297 title=\"5 occurrences\">#1297</a>}\n                  \"<span class=sf-dump-key>POSTforgot-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21298 title=\"4 occurrences\">#1298</a>}\n                  \"<span class=sf-dump-key>HEADreset-password/{token}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21299 title=\"5 occurrences\">#1299</a>}\n                  \"<span class=sf-dump-key>POSTreset-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21300 title=\"4 occurrences\">#1300</a>}\n                  \"<span class=sf-dump-key>HEADverify-email</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21301 title=\"5 occurrences\">#1301</a>}\n                  \"<span class=sf-dump-key>HEADverify-email/{id}/{hash}</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21302 title=\"5 occurrences\">#1302</a>}\n                  \"<span class=sf-dump-key>POSTemail/verification-notification</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21303 title=\"4 occurrences\">#1303</a>}\n                  \"<span class=sf-dump-key>HEADconfirm-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21304 title=\"5 occurrences\">#1304</a>}\n                  \"<span class=sf-dump-key>POSTconfirm-password</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21305 title=\"3 occurrences\">#1305</a>}\n                  \"<span class=sf-dump-key>PUTpassword</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21306 title=\"4 occurrences\">#1306</a>}\n                  \"<span class=sf-dump-key>POSTlogout</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21307 title=\"4 occurrences\">#1307</a>}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">nameList</span>: <span class=sf-dump-note>array:33</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>debugbar.openhandler</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2176 title=\"5 occurrences\">#176</a>}\n                  \"<span class=sf-dump-key>debugbar.clockwork</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2177 title=\"5 occurrences\">#177</a>}\n                  \"<span class=sf-dump-key>debugbar.assets.css</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2178 title=\"5 occurrences\">#178</a>}\n                  \"<span class=sf-dump-key>debugbar.assets.js</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2179 title=\"5 occurrences\">#179</a>}\n                  \"<span class=sf-dump-key>debugbar.cache.delete</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2180 title=\"4 occurrences\">#180</a>}\n                  \"<span class=sf-dump-key>debugbar.queries.explain</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2181 title=\"4 occurrences\">#181</a>}\n                  \"<span class=sf-dump-key>sanctum.csrf-cookie</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2183 title=\"5 occurrences\">#183</a>}\n                  \"<span class=sf-dump-key>ignition.healthCheck</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2196 title=\"5 occurrences\">#196</a>}\n                  \"<span class=sf-dump-key>ignition.executeSolution</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2197 title=\"4 occurrences\">#197</a>}\n                  \"<span class=sf-dump-key>ignition.updateConfig</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2198 title=\"4 occurrences\">#198</a>}\n                  \"<span class=sf-dump-key>dashboard</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2321 title=\"5 occurrences\">#321</a>}\n                  \"<span class=sf-dump-key>profile.edit</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2324 title=\"5 occurrences\">#324</a>}\n                  \"<span class=sf-dump-key>profile.update</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2325 title=\"4 occurrences\">#325</a>}\n                  \"<span class=sf-dump-key>profile.destroy</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2326 title=\"4 occurrences\">#326</a>}\n                  \"<span class=sf-dump-key>users.index</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21149 title=\"5 occurrences\">#1149</a>}\n                  \"<span class=sf-dump-key>users.create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21079 title=\"5 occurrences\">#1079</a>}\n                  \"<span class=sf-dump-key>users.store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21081 title=\"4 occurrences\">#1081</a>}\n                  \"<span class=sf-dump-key>users.show</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21080 title=\"5 occurrences\">#1080</a>}\n                  \"<span class=sf-dump-key>users.edit</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2671 title=\"5 occurrences\">#671</a>}\n                  \"<span class=sf-dump-key>users.update</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21030 title=\"5 occurrences\">#1030</a>}\n                  \"<span class=sf-dump-key>users.destroy</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21295 title=\"4 occurrences\">#1295</a>}\n                  \"<span class=sf-dump-key>register</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2328 title=\"5 occurrences\">#328</a>}\n                  \"<span class=sf-dump-key>login</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2329 title=\"5 occurrences\">#329</a>}\n                  \"<span class=sf-dump-key>password.request</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21297 title=\"5 occurrences\">#1297</a>}\n                  \"<span class=sf-dump-key>password.email</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21298 title=\"4 occurrences\">#1298</a>}\n                  \"<span class=sf-dump-key>password.reset</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21299 title=\"5 occurrences\">#1299</a>}\n                  \"<span class=sf-dump-key>password.store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21300 title=\"4 occurrences\">#1300</a>}\n                  \"<span class=sf-dump-key>verification.notice</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21301 title=\"5 occurrences\">#1301</a>}\n                  \"<span class=sf-dump-key>verification.verify</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21302 title=\"5 occurrences\">#1302</a>}\n                  \"<span class=sf-dump-key>verification.send</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21303 title=\"4 occurrences\">#1303</a>}\n                  \"<span class=sf-dump-key>password.confirm</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21304 title=\"5 occurrences\">#1304</a>}\n                  \"<span class=sf-dump-key>password.update</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21306 title=\"4 occurrences\">#1306</a>}\n                  \"<span class=sf-dump-key>logout</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21307 title=\"4 occurrences\">#1307</a>}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">actionList</span>: <span class=sf-dump-note>array:36</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@handle</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2176 title=\"5 occurrences\">#176</a>}\n                  \"<span class=sf-dump-key>Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@clockwork</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2177 title=\"5 occurrences\">#177</a>}\n                  \"<span class=sf-dump-key>Barryvdh\\Debugbar\\Controllers\\AssetController@css</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2178 title=\"5 occurrences\">#178</a>}\n                  \"<span class=sf-dump-key>Barryvdh\\Debugbar\\Controllers\\AssetController@js</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2179 title=\"5 occurrences\">#179</a>}\n                  \"<span class=sf-dump-key>Barryvdh\\Debugbar\\Controllers\\CacheController@delete</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2180 title=\"4 occurrences\">#180</a>}\n                  \"<span class=sf-dump-key>Barryvdh\\Debugbar\\Controllers\\QueriesController@explain</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2181 title=\"4 occurrences\">#181</a>}\n                  \"<span class=sf-dump-key>Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2183 title=\"5 occurrences\">#183</a>}\n                  \"<span class=sf-dump-key>Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2196 title=\"5 occurrences\">#196</a>}\n                  \"<span class=sf-dump-key>Spatie\\LaravelIgnition\\Http\\Controllers\\ExecuteSolutionController</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2197 title=\"4 occurrences\">#197</a>}\n                  \"<span class=sf-dump-key>Spatie\\LaravelIgnition\\Http\\Controllers\\UpdateConfigController</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2198 title=\"4 occurrences\">#198</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\DashboardController@index</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2321 title=\"5 occurrences\">#321</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\ProfileController@edit</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2324 title=\"5 occurrences\">#324</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\ProfileController@update</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2325 title=\"4 occurrences\">#325</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\ProfileController@destroy</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2326 title=\"4 occurrences\">#326</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\UserController@index</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21149 title=\"5 occurrences\">#1149</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\UserController@create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21079 title=\"5 occurrences\">#1079</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\UserController@store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21081 title=\"4 occurrences\">#1081</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\UserController@show</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21080 title=\"5 occurrences\">#1080</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\UserController@edit</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2671 title=\"5 occurrences\">#671</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\UserController@update</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21030 title=\"5 occurrences\">#1030</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\UserController@destroy</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21295 title=\"4 occurrences\">#1295</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\RegisteredUserController@create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2328 title=\"5 occurrences\">#328</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\RegisteredUserController@store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2327 title=\"3 occurrences\">#327</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2329 title=\"5 occurrences\">#329</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21296 title=\"4 occurrences\">#1296</a> &#8230;}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\PasswordResetLinkController@create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21297 title=\"5 occurrences\">#1297</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\PasswordResetLinkController@store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21298 title=\"4 occurrences\">#1298</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\NewPasswordController@create</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21299 title=\"5 occurrences\">#1299</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\NewPasswordController@store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21300 title=\"4 occurrences\">#1300</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\EmailVerificationPromptController</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21301 title=\"5 occurrences\">#1301</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\VerifyEmailController</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21302 title=\"5 occurrences\">#1302</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\EmailVerificationNotificationController@store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21303 title=\"4 occurrences\">#1303</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\ConfirmablePasswordController@show</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21304 title=\"5 occurrences\">#1304</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\ConfirmablePasswordController@store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21305 title=\"3 occurrences\">#1305</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\PasswordController@update</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21306 title=\"4 occurrences\">#1306</a>}\n                  \"<span class=sf-dump-key>App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@destroy</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Route</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21307 title=\"4 occurrences\">#1307</a>}\n                </samp>]\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">request</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Request</span></span> {<a class=sf-dump-ref>#37</a><samp data-depth=8 class=sf-dump-compact>\n                +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ParameterBag</span></span> {<a class=sf-dump-ref>#42</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n                </samp>}\n                +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">InputBag</span></span> {<a class=sf-dump-ref>#38</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n                    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n                    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                  </samp>]\n                </samp>}\n                +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">InputBag</span></span> {<a class=sf-dump-ref>#45</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n                </samp>}\n                +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ServerBag</span></span> {<a class=sf-dump-ref>#40</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"62 characters\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/public</span>\"\n                    \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n                    \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50667</span>\"\n                    \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"30 characters\">PHP/8.4.5 (Development Server)</span>\"\n                    \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n                    \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n                    \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n                    \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n                    \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                    \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n                    \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"72 characters\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/public/index.php</span>\"\n                    \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n                    \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n                    \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n                    \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n                    \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n                    \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n                    \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n                    \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n                    \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n                    \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n                    \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n                    \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n                    \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8001/login</span>\"\n                    \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n                    \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n                    \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n                    \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n                    \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n                    \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1001 characters\">XSRF-TOKEN=eyJpdiI6Ikx1QlMxT2xsMGdkZUdOaS9taHdJamc9PSIsInZhbHVlIjoiNHc4TTVIL1RVWkFaaUo5TXhkc2lmWnBrRGZxSlIxVC9nQ2tsbklOTnNyWWFnK1ZheGk3ZmZqQmJlTVV3M2doSFEzUjRncWNuY0tzY3h1TnVkQ3FOQWs0V3dWd21XUU9FeDJXVEMzN1Qwck9Cd3lnNEpwcXpZblgwSXA0WTBLaG4iLCJtYWMiOiJjYmJlYjQ1OWU3YjljNDIyM2NhYmMzMmZiMDI3YzFmOWVlOWYwNDYxNWY0MzgyNjdlOTQ4YTg2NmE0YWFhNTA5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRXYW5XRU92eWU2Y1FFd2ZFWkdUZ0E9PSIsInZhbHVlIjoiM0JHSU1md3RaS0ZhSkJlTkRKK3hhM1VVa1dmSHBMMG5mci9lYkNnTEpybzlRNHpFbGdkSXBObFY3R01VNUhRbnl1WVRPUWNOd1QzOGcxa2lYVExOdnZtK0tlV3ZndE1jbUt1bEpLaUFlVlJzZER2VVZmRnovdDB1andnK2ZnUWIiLCJtYWMiOiJmYzAwMjA3ZjRhOGFmYTkxODdmZGRkNjZjYTg0N2NkNmY5NWNjYWM5MDc2ZDAzYmY5NDkxMGI5YjQ1NmM4NDE5IiwidGFnIjoiIn0%3D; _ga_Z28525QR15=GS2.1.s1749595141$o5$g0$t1749595141$j60$l0$h0; _ga=GA1.1.E20B8A103D1D49E6A741E19936EFAC67; localauth=localapi9d9bde7eb655a1ef:; _ga_CJB9T8MR54=GS2.1.s1749564387$o1$g1$t1749564392$j55$l0$h0; _ga_WEZVWJKCXR=GS2.1.s1749564388$o1$g0$t1749564388$j60$l0$h0; isNotIncognito=true</span>\"\n                    \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.3228</span>\n                    \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n                  </samp>]\n                </samp>}\n                +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FileBag</span></span> {<a class=sf-dump-ref>#44</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n                </samp>}\n                +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">InputBag</span></span> {<a class=sf-dump-ref>#43</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n                    \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MPFbKSj1cMFroXoAqBuGEKE7S5ajt4nbmlFoNSOe</span>\"\n                    \"<span class=sf-dump-key>_ga_Z28525QR15</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>localauth</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>_ga_CJB9T8MR54</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>_ga_WEZVWJKCXR</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>isNotIncognito</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                </samp>}\n                +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HeaderBag</span></span> {<a class=sf-dump-ref>#39</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8001/login</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n                    </samp>]\n                    \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1001 characters\">XSRF-TOKEN=eyJpdiI6Ikx1QlMxT2xsMGdkZUdOaS9taHdJamc9PSIsInZhbHVlIjoiNHc4TTVIL1RVWkFaaUo5TXhkc2lmWnBrRGZxSlIxVC9nQ2tsbklOTnNyWWFnK1ZheGk3ZmZqQmJlTVV3M2doSFEzUjRncWNuY0tzY3h1TnVkQ3FOQWs0V3dWd21XUU9FeDJXVEMzN1Qwck9Cd3lnNEpwcXpZblgwSXA0WTBLaG4iLCJtYWMiOiJjYmJlYjQ1OWU3YjljNDIyM2NhYmMzMmZiMDI3YzFmOWVlOWYwNDYxNWY0MzgyNjdlOTQ4YTg2NmE0YWFhNTA5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRXYW5XRU92eWU2Y1FFd2ZFWkdUZ0E9PSIsInZhbHVlIjoiM0JHSU1md3RaS0ZhSkJlTkRKK3hhM1VVa1dmSHBMMG5mci9lYkNnTEpybzlRNHpFbGdkSXBObFY3R01VNUhRbnl1WVRPUWNOd1QzOGcxa2lYVExOdnZtK0tlV3ZndE1jbUt1bEpLaUFlVlJzZER2VVZmRnovdDB1andnK2ZnUWIiLCJtYWMiOiJmYzAwMjA3ZjRhOGFmYTkxODdmZGRkNjZjYTg0N2NkNmY5NWNjYWM5MDc2ZDAzYmY5NDkxMGI5YjQ1NmM4NDE5IiwidGFnIjoiIn0%3D; _ga_Z28525QR15=GS2.1.s1749595141$o5$g0$t1749595141$j60$l0$h0; _ga=GA1.1.E20B8A103D1D49E6A741E19936EFAC67; localauth=localapi9d9bde7eb655a1ef:; _ga_CJB9T8MR54=GS2.1.s1749564387$o1$g1$t1749564392$j55$l0$h0; _ga_WEZVWJKCXR=GS2.1.s1749564388$o1$g0$t1749564388$j60$l0$h0; isNotIncognito=true</span>\"\n                    </samp>]\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n                </samp>}\n                #<span class=sf-dump-protected title=\"Protected property\">content</span>: \"<span class=sf-dump-str title=\"88 characters\">_token=k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX&amp;email=test%40gmail.com&amp;password=password</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"21 characters\">application/xhtml+xml</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">application/xml</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n                #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Session\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21452 title=\"3 occurrences\">#1452</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isSafeContentPreferred</span>: <span class=sf-dump-const title=\"Uninitialized property\">? bool</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">trustedValuesCache</span>: []\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isIisRewrite</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">InputBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21486 title=\"2 occurrences\">#1486</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21405 title=\"2 occurrences\">#1405</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21422 title=\"2 occurrences\">#1422</a>}\n                <span class=sf-dump-meta>basePath</span>: \"\"\n                <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">assetRoot</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">forcedRoot</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">forceScheme</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">cachedRoot</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">cachedScheme</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">rootNamespace</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">sessionResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#1474</a><samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RoutingServiceProvider\n41 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">RoutingServiceProvider</span></span>\"\n                <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RoutingServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">RoutingServiceProvider</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref210 title=\"2 occurrences\">#10</a> &#8230;}\n                <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php\n130 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span><span class=\"sf-dump-ellipsis-tail\">src/Illuminate/Routing/RoutingServiceProvider.php</span></span>\"\n                <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">75 to 77</span>\"\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">keyResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#1473</a><samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RoutingServiceProvider\n41 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">RoutingServiceProvider</span></span>\"\n                <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\RoutingServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">RoutingServiceProvider</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref210 title=\"2 occurrences\">#10</a> &#8230;}\n                <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Routing/RoutingServiceProvider.php\n130 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span><span class=\"sf-dump-ellipsis-tail\">src/Illuminate/Routing/RoutingServiceProvider.php</span></span>\"\n                <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">79 to 81</span>\"\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">missingNamedRouteResolver</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">formatHostUsing</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">formatPathUsing</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">routeGenerator</span>: <span class=sf-dump-const>null</span>\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Session\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21452 title=\"3 occurrences\">#1452</a>}\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">redirect</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">redirectRoute</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">redirectAction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">errorBag</span>: \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">stopOnFirstFailure</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">validator</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Validation\\Validator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Validation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Validator</span></span> {<a class=sf-dump-ref>#1496</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">translator</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Translation\\Translator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Translation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Translator</span></span> {<a class=sf-dump-ref>#1494</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">parsed</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>auth.failed</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">failed</span>\"\n                </samp>]\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">loader</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Translation\\FileLoader\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Translation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FileLoader</span></span> {<a class=sf-dump-ref>#1493</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">files</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Filesystem\\Filesystem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Filesystem</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Filesystem</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2174 title=\"2 occurrences\">#174</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">paths</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"112 characters\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Translation/lang</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"60 characters\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/lang</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">jsonPaths</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">hints</span>: []\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">locale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">fallback</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">loaded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>*</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>*</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>en</span>\" => []\n                  </samp>]\n                  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>failed</span>\" => \"<span class=sf-dump-str title=\"43 characters\">These credentials do not match our records.</span>\"\n                      \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The provided password is incorrect.</span>\"\n                      \"<span class=sf-dump-key>throttle</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Too many login attempts. Please try again in :seconds seconds.</span>\"\n                    </samp>]\n                  </samp>]\n                </samp>]\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">selector</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">determineLocalesUsing</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">stringableHandlers</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">missingTranslationKeyCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">handleMissingTranslationKeys</span>: <span class=sf-dump-const>true</span>\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n            #<span class=sf-dump-protected title=\"Protected property\">presenceVerifier</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Validation\\DatabasePresenceVerifier\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Validation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabasePresenceVerifier</span></span> {<a class=sf-dump-ref>#1495</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">db</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2156 title=\"2 occurrences\">#156</a><samp data-depth=8 id=sf-dump-**********-ref2156 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">app</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                #<span class=sf-dump-protected title=\"Protected property\">factory</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Connectors\\ConnectionFactory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Connectors</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ConnectionFactory</span></span> {<a class=sf-dump-ref>#50</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"42 occurrences\">#2</a> &#8230;40}\n                </samp>}\n                #<span class=sf-dump-protected title=\"Protected property\">connections</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>sqlite</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\SQLiteConnection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">SQLiteConnection</span></span> {<a class=sf-dump-ref>#1502</a> &#8230;25}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">extensions</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">reconnector</span>: <span class=sf-dump-note>Closure($connection)</span> {<a class=sf-dump-ref>#150</a><samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span>\"\n                  <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2156 title=\"2 occurrences\">#156</a>}\n                  <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Database/DatabaseManager.php\n124 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span><span class=\"sf-dump-ellipsis-tail\">src/Illuminate/Database/DatabaseManager.php</span></span>\"\n                  <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">79 to 81</span>\"\n                </samp>}\n                #<span class=sf-dump-protected title=\"Protected property\">doctrineTypes</span>: []\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">failedRules</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">excludeAttributes</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#1500</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">messages</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">data</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n              \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n              \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">initialRules</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>password</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">rules</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>password</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">currentRule</span>: \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">implicitAttributes</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">implicitAttributesFormatter</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">distinctValues</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">after</span>: []\n            +<span class=sf-dump-public title=\"Public property\">customMessages</span>: []\n            +<span class=sf-dump-public title=\"Public property\">fallbackMessages</span>: []\n            +<span class=sf-dump-public title=\"Public property\">customAttributes</span>: []\n            +<span class=sf-dump-public title=\"Public property\">customValues</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">stopOnFirstFailure</span>: <span class=sf-dump-const>false</span>\n            +<span class=sf-dump-public title=\"Public property\">excludeUnvalidatedArrayKeys</span>: <span class=sf-dump-const>true</span>\n            +<span class=sf-dump-public title=\"Public property\">extensions</span>: []\n            +<span class=sf-dump-public title=\"Public property\">replacers</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">fileRules</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Between</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">Dimensions</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">Extensions</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">File</span>\"\n              <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n              <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"3 characters\">Max</span>\"\n              <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">Mimes</span>\"\n              <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">Mimetypes</span>\"\n              <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"3 characters\">Min</span>\"\n              <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"4 characters\">Size</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">implicitRules</span>: <span class=sf-dump-note>array:23</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Accepted</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">AcceptedIf</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">Declined</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">DeclinedIf</span>\"\n              <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">Filled</span>\"\n              <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">Missing</span>\"\n              <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">MissingIf</span>\"\n              <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">MissingUnless</span>\"\n              <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">MissingWith</span>\"\n              <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">MissingWithAll</span>\"\n              <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">Present</span>\"\n              <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">PresentIf</span>\"\n              <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">PresentUnless</span>\"\n              <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">PresentWith</span>\"\n              <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"14 characters\">PresentWithAll</span>\"\n              <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"8 characters\">Required</span>\"\n              <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">RequiredIf</span>\"\n              <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredIfAccepted</span>\"\n              <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"14 characters\">RequiredUnless</span>\"\n              <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">RequiredWith</span>\"\n              <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithAll</span>\"\n              <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithout</span>\"\n              <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredWithoutAll</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dependentRules</span>: <span class=sf-dump-note>array:37</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">After</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">AfterOrEqual</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">Before</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"13 characters\">BeforeOrEqual</span>\"\n              <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">Confirmed</span>\"\n              <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">Different</span>\"\n              <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">ExcludeIf</span>\"\n              <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">ExcludeUnless</span>\"\n              <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">ExcludeWith</span>\"\n              <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">ExcludeWithout</span>\"\n              <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"2 characters\">Gt</span>\"\n              <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3 characters\">Gte</span>\"\n              <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"2 characters\">Lt</span>\"\n              <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"3 characters\">Lte</span>\"\n              <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">AcceptedIf</span>\"\n              <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">DeclinedIf</span>\"\n              <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">RequiredIf</span>\"\n              <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredIfAccepted</span>\"\n              <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"14 characters\">RequiredUnless</span>\"\n              <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">RequiredWith</span>\"\n              <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithAll</span>\"\n              <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithout</span>\"\n              <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredWithoutAll</span>\"\n              <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"9 characters\">PresentIf</span>\"\n              <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"13 characters\">PresentUnless</span>\"\n              <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"11 characters\">PresentWith</span>\"\n              <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"14 characters\">PresentWithAll</span>\"\n              <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"10 characters\">Prohibited</span>\"\n              <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"12 characters\">ProhibitedIf</span>\"\n              <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"16 characters\">ProhibitedUnless</span>\"\n              <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"9 characters\">Prohibits</span>\"\n              <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"9 characters\">MissingIf</span>\"\n              <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"13 characters\">MissingUnless</span>\"\n              <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"11 characters\">MissingWith</span>\"\n              <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"14 characters\">MissingWithAll</span>\"\n              <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"4 characters\">Same</span>\"\n              <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"6 characters\">Unique</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">excludeRules</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Exclude</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">ExcludeIf</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">ExcludeUnless</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">ExcludeWith</span>\"\n              <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">ExcludeWithout</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">sizeRules</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">Size</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Between</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">Min</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"3 characters\">Max</span>\"\n              <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"2 characters\">Gt</span>\"\n              <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"2 characters\">Lt</span>\"\n              <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">Gte</span>\"\n              <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"3 characters\">Lte</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">numericRules</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Numeric</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Integer</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">Decimal</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">defaultNumericRules</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Numeric</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Integer</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">Decimal</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">exception</span>: \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Validation\\ValidationException</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">ensureExponentWithinAllowedRangeUsing</span>: <span class=sf-dump-const>null</span>\n          </samp>}\n          <span class=sf-dump-meta>pathInfo</span>: \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n          <span class=sf-dump-meta>requestUri</span>: \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n          <span class=sf-dump-meta>baseUrl</span>: \"\"\n          <span class=sf-dump-meta>basePath</span>: \"\"\n          <span class=sf-dump-meta>method</span>: \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n          <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>259</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"65 characters\">[object App\\Http\\Controllers\\Auth\\AuthenticatedSessionController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>205</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>806</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"62 characters\">{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"47 characters\">app/Http/Middleware/RedirectIfAuthenticated.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>28</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"64 characters\">{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">App\\Http\\Middleware\\RedirectIfAuthenticated</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"67 characters\">{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"64 characters\">{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/public/index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["     */\n", "    public static function withMessages(array $messages)\n", "    {\n", "        return new static(tap(ValidatorFacade::make([], []), function ($validator) use ($messages) {\n", "            foreach ($messages as $key => $value) {\n", "                foreach (Arr::wrap($value) as $message) {\n", "                    $validator->errors()->add($key, $message);\n"], "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FValidationException.php:71", "ajax": false, "filename": "ValidationException.php", "line": "71"}}]}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:26-33</a>"}, "queries": {"count": 2, "nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00213, "accumulated_duration_str": "2.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 167}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 127}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/app/Http/Requests/Auth/LoginRequest.php", "line": 44}], "start": **********.744988, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:167", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 167}, "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:167", "ajax": false, "filename": "EloquentUserProvider.php", "line": "167"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"users\" where \"email\" = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/app/Http/Requests/Auth/LoginRequest.php", "line": 44}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/app/Http/Controllers/Auth/AuthenticatedSessionController.php", "line": 28}], "start": **********.762636, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "cache": {"start": **********.322814, "end": **********.040872, "duration": 0.7180581092834473, "duration_str": "718ms", "measures": [{"label": "missed\t<EMAIL>|127.0.0.1", "start": **********.706513, "relative_start": 0.3836989402770996, "end": **********.706513, "relative_end": **********.706513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": {"key": "<EMAIL>|127.0.0.1", "tags": []}, "collector": null, "group": null}], "count": 1, "nb_measures": 1}, "session": {"_token": "k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/register\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:2 [\n    0 => \"_old_input\"\n    1 => \"errors\"\n  ]\n]", "_old_input": "array:2 [\n  \"_token\" => \"k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX\"\n  \"email\" => \"<EMAIL>\"\n]", "errors": "Illuminate\\Support\\ViewErrorBag {#1504\n  #bags: array:1 [\n    \"default\" => Illuminate\\Support\\MessageBag {#1540\n      #messages: array:1 [\n        \"email\" => array:1 [\n          0 => \"These credentials do not match our records.\"\n        ]\n      ]\n      #format: \":message\"\n    }\n  ]\n}"}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8001/login", "action_name": null, "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "uri": "POST login", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:26-33</a>", "middleware": "web, guest", "duration": "717ms", "peak_memory": "12MB", "response": "Redirect to http://127.0.0.1:8001/login", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1210289811 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8001/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1001 characters\">XSRF-TOKEN=eyJpdiI6Ikx1QlMxT2xsMGdkZUdOaS9taHdJamc9PSIsInZhbHVlIjoiNHc4TTVIL1RVWkFaaUo5TXhkc2lmWnBrRGZxSlIxVC9nQ2tsbklOTnNyWWFnK1ZheGk3ZmZqQmJlTVV3M2doSFEzUjRncWNuY0tzY3h1TnVkQ3FOQWs0V3dWd21XUU9FeDJXVEMzN1Qwck9Cd3lnNEpwcXpZblgwSXA0WTBLaG4iLCJtYWMiOiJjYmJlYjQ1OWU3YjljNDIyM2NhYmMzMmZiMDI3YzFmOWVlOWYwNDYxNWY0MzgyNjdlOTQ4YTg2NmE0YWFhNTA5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRXYW5XRU92eWU2Y1FFd2ZFWkdUZ0E9PSIsInZhbHVlIjoiM0JHSU1md3RaS0ZhSkJlTkRKK3hhM1VVa1dmSHBMMG5mci9lYkNnTEpybzlRNHpFbGdkSXBObFY3R01VNUhRbnl1WVRPUWNOd1QzOGcxa2lYVExOdnZtK0tlV3ZndE1jbUt1bEpLaUFlVlJzZER2VVZmRnovdDB1andnK2ZnUWIiLCJtYWMiOiJmYzAwMjA3ZjRhOGFmYTkxODdmZGRkNjZjYTg0N2NkNmY5NWNjYWM5MDc2ZDAzYmY5NDkxMGI5YjQ1NmM4NDE5IiwidGFnIjoiIn0%3D; _ga_Z28525QR15=GS2.1.s1749595141$o5$g0$t1749595141$j60$l0$h0; _ga=GA1.1.E20B8A103D1D49E6A741E19936EFAC67; localauth=localapi9d9bde7eb655a1ef:; _ga_CJB9T8MR54=GS2.1.s1749564387$o1$g1$t1749564392$j55$l0$h0; _ga_WEZVWJKCXR=GS2.1.s1749564388$o1$g0$t1749564388$j60$l0$h0; isNotIncognito=true</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210289811\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1447447125 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MPFbKSj1cMFroXoAqBuGEKE7S5ajt4nbmlFoNSOe</span>\"\n  \"<span class=sf-dump-key>_ga_Z28525QR15</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>localauth</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_CJB9T8MR54</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_WEZVWJKCXR</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>isNotIncognito</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447447125\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1447587318 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 09:39:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8001/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447587318\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1668147981 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8001/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">_old_input</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">errors</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_old_input</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n  </samp>]\n  \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref>#1504</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">bags</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#1540</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">These credentials do not match our records.</span>\"\n          </samp>]\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n      </samp>}\n    </samp>]\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668147981\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8001/login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store"}, "badge": "302 Found"}}