{"__meta": {"id": "01JY6B9M5AR7RTM85PGQ5P5GAM", "datetime": "2025-06-20 09:39:09", "utime": **********.610796, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.4.5", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.586074, "end": **********.610806, "duration": 0.024731874465942383, "duration_str": "24.73ms", "measures": [{"label": "Booting", "start": **********.586074, "relative_start": 0, "end": **********.591182, "relative_end": **********.591182, "duration": 0.005107879638671875, "duration_str": "5.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.59119, "relative_start": 0.005115985870361328, "end": **********.610807, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "19.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.591946, "relative_start": 0.005871772766113281, "end": **********.592389, "relative_end": **********.592389, "duration": 0.0004432201385498047, "duration_str": "443μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.599243, "relative_start": 0.013168811798095703, "end": **********.610679, "relative_end": **********.610679, "duration": 0.011435985565185547, "duration_str": "11.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: auth.login", "start": **********.599346, "relative_start": 0.013271808624267578, "end": **********.599346, "relative_end": **********.599346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.auth-session-status", "start": **********.603426, "relative_start": 0.017351865768432617, "end": **********.603426, "relative_end": **********.603426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.605445, "relative_start": 0.01937079429626465, "end": **********.605445, "relative_end": **********.605445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.605992, "relative_start": 0.019917964935302734, "end": **********.605992, "relative_end": **********.605992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.606469, "relative_start": 0.02039480209350586, "end": **********.606469, "relative_end": **********.606469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.606993, "relative_start": 0.020918846130371094, "end": **********.606993, "relative_end": **********.606993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.607067, "relative_start": 0.02099299430847168, "end": **********.607067, "relative_end": **********.607067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.607143, "relative_start": 0.021068811416625977, "end": **********.607143, "relative_end": **********.607143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.primary-button", "start": **********.607303, "relative_start": 0.021228790283203125, "end": **********.607303, "relative_end": **********.607303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.guest", "start": **********.607702, "relative_start": 0.02162790298461914, "end": **********.607702, "relative_end": **********.607702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.application-logo", "start": **********.610287, "relative_start": 0.02421283721923828, "end": **********.610287, "relative_end": **********.610287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 2009656, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": **********.599337, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fauth%2Flogin.blade.php:1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "components.auth-session-status", "param_count": null, "params": [], "start": **********.603419, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/auth-session-status.blade.phpcomponents.auth-session-status", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fauth-session-status.blade.php:1", "ajax": false, "filename": "auth-session-status.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.605438, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.605985, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.606462, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.606987, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.607062, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.607138, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.primary-button", "param_count": null, "params": [], "start": **********.607297, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/primary-button.blade.phpcomponents.primary-button", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fprimary-button.blade.php:1", "ajax": false, "filename": "primary-button.blade.php", "line": "?"}}, {"name": "layouts.guest", "param_count": null, "params": [], "start": **********.607695, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/layouts/guest.blade.phplayouts.guest", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Flayouts%2Fguest.blade.php:1", "ajax": false, "filename": "guest.blade.php", "line": "?"}}, {"name": "components.application-logo", "param_count": null, "params": [], "start": **********.61028, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/application-logo.blade.phpcomponents.application-logo", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fapplication-logo.blade.php:1", "ajax": false, "filename": "application-logo.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "login", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "cache": {"start": **********.586074, "end": **********.611013, "duration": 0.02493882179260254, "duration_str": "24.94ms", "measures": [], "count": 0, "nb_measures": 0}, "session": {"_token": "k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01JY6B9JJXYQHDSYA4GTJCVVFW\" => null\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/register\"\n]", "_flash": "array:2 [\n  \"old\" => array:2 [\n    0 => \"_old_input\"\n    1 => \"errors\"\n  ]\n  \"new\" => []\n]", "_old_input": "array:2 [\n  \"_token\" => \"k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX\"\n  \"email\" => \"<EMAIL>\"\n]", "errors": "Illuminate\\Support\\ViewErrorBag {#1464\n  #bags: array:1 [\n    \"default\" => Illuminate\\Support\\MessageBag {#1465\n      #messages: array:1 [\n        \"email\" => array:1 [\n          0 => \"These credentials do not match our records.\"\n        ]\n      ]\n      #format: \":message\"\n    }\n  ]\n}"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "uri": "GET login", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>", "middleware": "web, guest", "duration": "25.07ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1351403505 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1351403505\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8001/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1001 characters\">XSRF-TOKEN=eyJpdiI6Imw1bDVQZ3UyVk40blExdDNURzRWdUE9PSIsInZhbHVlIjoiTFVrdFBZOUFTWk5VMVZnSU9sTGlmZit4OHd5Zkc4Yk1JK3hDa1BPMkNZTzJOTkZaNHlUYytuTk4vZFNWUk9uN1Z6TjRBWVNna3hhc1lsK2VaUktSUzVuT2M3bDhCRXVUWDNLS2dzVnJZYVpocEtKNFU1eU12SEx6eEh3VSs3L1MiLCJtYWMiOiJmZjBhNzQ4ZDYzNzM3YWY4MTI5MzkyNjkxMzM4ZGM5OGU2ODQxOGU4NWNmYTQxZGJmMTE5MDA2NDM2MGZjZmU4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkFaa2txVXhib0pnb1dqeHlOTk1Tenc9PSIsInZhbHVlIjoiQ2RvbGVCVGlybUxKa0Y5UmIwSk9JUCtQYTM0cXl4YmpFaVNXV2o3cjhGcUFFQ2h3Sm1OVXg5NUJnUjl3WGU2aXI0NzRabkFMQWxDWWZycWhpN2dicm1rZHoyUmhnTEtFRVlJZndQSEVHOTVxYnFENEdLYXVmYTBPNGxueHdoRHAiLCJtYWMiOiJhMjNjOWJiYjk5MzkzZDgxMmMwMTRjMzFkOWQ2MzBhMDVjMTNhZDdhMmEwZDI4MDBjMjdhN2RjNmIyODM5M2RhIiwidGFnIjoiIn0%3D; _ga_Z28525QR15=GS2.1.s1749595141$o5$g0$t1749595141$j60$l0$h0; _ga=GA1.1.E20B8A103D1D49E6A741E19936EFAC67; localauth=localapi9d9bde7eb655a1ef:; _ga_CJB9T8MR54=GS2.1.s1749564387$o1$g1$t1749564392$j55$l0$h0; _ga_WEZVWJKCXR=GS2.1.s1749564388$o1$g0$t1749564388$j60$l0$h0; isNotIncognito=true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1875946070 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MPFbKSj1cMFroXoAqBuGEKE7S5ajt4nbmlFoNSOe</span>\"\n  \"<span class=sf-dump-key>_ga_Z28525QR15</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>localauth</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_CJB9T8MR54</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_WEZVWJKCXR</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>isNotIncognito</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875946070\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2096071310 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 09:39:09 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096071310\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-771867240 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JY6B9JJXYQHDSYA4GTJCVVFW</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8001/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">_old_input</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">errors</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_old_input</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n  </samp>]\n  \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref>#1464</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">bags</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#1465</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">These credentials do not match our records.</span>\"\n          </samp>]\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n      </samp>}\n    </samp>]\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771867240\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create"}, "badge": null}}