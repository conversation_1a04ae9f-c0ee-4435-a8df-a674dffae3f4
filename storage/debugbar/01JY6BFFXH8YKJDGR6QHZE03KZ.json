{"__meta": {"id": "01JY6BFFXH8YKJDGR6QHZE03KZ", "datetime": "2025-06-20 09:42:21", "utime": **********.873774, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.577566, "end": **********.873788, "duration": 0.2962222099304199, "duration_str": "296ms", "measures": [{"label": "Booting", "start": **********.577566, "relative_start": 0, "end": **********.589477, "relative_end": **********.589477, "duration": 0.011911153793334961, "duration_str": "11.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.589487, "relative_start": 0.011921167373657227, "end": **********.873789, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.590862, "relative_start": 0.013296127319335938, "end": **********.591116, "relative_end": **********.591116, "duration": 0.00025391578674316406, "duration_str": "254μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.873437, "relative_start": 0.****************, "end": **********.873498, "relative_end": **********.873498, "duration": 6.103515625e-05, "duration_str": "61μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 3392376, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:26-33</a>"}, "queries": {"count": 2, "nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00415, "accumulated_duration_str": "4.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 167}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 127}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "line": 340}, {"index": 18, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/app/Http/Requests/Auth/LoginRequest.php", "line": 44}], "start": **********.640733, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:167", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 167}, "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:167", "ajax": false, "filename": "EloquentUserProvider.php", "line": "167"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"users\" where \"email\" = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Requests/Auth/LoginRequest.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/app/Http/Requests/Auth/LoginRequest.php", "line": 44}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/app/Http/Controllers/Auth/AuthenticatedSessionController.php", "line": 28}], "start": **********.654063, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:6 [\n    \"id\" => 1\n    \"name\" => \"Administrateur\"\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-06-20T09:37:23.000000Z\"\n    \"created_at\" => \"2025-06-20T09:37:23.000000Z\"\n    \"updated_at\" => \"2025-06-20T09:37:23.000000Z\"\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "cache": {"start": **********.577566, "end": **********.875101, "duration": 0.2975351810455322, "duration_str": "298ms", "measures": [{"label": "missed\t<EMAIL>|127.0.0.1", "start": **********.613084, "relative_start": 0.03551816940307617, "end": **********.613084, "relative_end": **********.613084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": {"key": "<EMAIL>|127.0.0.1", "tags": []}, "collector": null, "group": null}], "count": 1, "nb_measures": 1}, "session": {"_token": "8eamzkV8SOaJroNqAGBwmG7MJXSsEoksDtP9bWIf", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/login", "action_name": null, "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "uri": "POST login", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:26-33</a>", "middleware": "web, guest", "duration": "297ms", "peak_memory": "4MB", "response": "Redirect to http://127.0.0.1:8000/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1070427898 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zzVSDeYmFGWpwiasHinRVBC9EBpZMGxQmBVe3QVz</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"18 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070427898\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-992676245 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">95</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1255 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InF3QWQ4N2NBNnJDN3FISlV6V3BPaFE9PSIsInZhbHVlIjoiNmhpUC96NWFnU2t4UENTUWZlakd2VFREaGp0Z2ovTUtnUC8xY2luc0JqUGgrSlhuTHBuUWVRYktTUUt5MGp4TzR6S1c0dDdXL0xYR29lWExnSlRiMlR3TTBzWTBRcXBZUGhNWkY0S0c1NHlieDhmS2paZGhuRVA4MmdkVlFlaExQRjhYVUlLSk5lL0JZeTdUbGI2dkZqUlRMcUswMVVkRm5TUVI0Nlhvck1yWGtQRzRyTGx4allsK0l5U3V2djRUMmJMZ2ZZc2RxN2o2WXdjOFJ0MVF0UW9YN21zN0tybDFZSlEreFd2SFRkbz0iLCJtYWMiOiI2NWI3ZDY1MTg1NGFlOGRiNDY3NzE3MjE1Y2M5M2Y0Y2EyMDcxOTFlNDczZTMwYmVhOGViM2FhOTMwMjJkMGVmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlhBeW9JZCs0TG1lK1NpVWE0T0wyanc9PSIsInZhbHVlIjoiUGREY2pVK0xyZmNBRytnNHE3SkUzSnNSVVluZEdGSUFaZGRVNFdXS0RNU1lhRURzS01tV1JMNlcrY24vVEM0VGpCeDlMSXdhZFNydGwyeG9sbjUzVTM1bUVTZWo4MWFKQncwUTlCVDR0aFlXSFRwMFN2ckg1dm9Tc3duY0VmdU0iLCJtYWMiOiI4ZDFhOTFmODBkMDk5NWI4NTJmZjNkYzdiOWE2ZWIxNzkyYWYwY2I3YWU0M2QwNmFhYjdiMDA0NDI3MjM3MTc3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNuQkt0bkIrTDBVZU91T1hUd1cwdkE9PSIsInZhbHVlIjoidXF0a1JXR05XVTFMUHR4L1JvN1drYmY2S2J2VjZSNEp6Mm8zS2JzWjFEdlowUkFnRkpOYktndUZnR0k2czJmZm84ZDdVUGV2MW0rRUhpZ1lMMzNlT3N4MFQvc2tncHNmRVcyR21wd2IxMm9XRmdBNkxUbm92SGtqajdIS2JIWEoiLCJtYWMiOiI2MjkyNDY5YWZmZGE5ODNjMDgyMTYyN2NkOWYyYWM5MGRjMWI4ZDhiMThkYzkyMzZiOTM2Mjg3YmQzYjI4NGNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-992676245\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1764662028 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zzVSDeYmFGWpwiasHinRVBC9EBpZMGxQmBVe3QVz</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bf6sPAUN440Zpd2VPpBPC2A8V9S0KUF05WmHPeqm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1764662028\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-950795288 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 09:42:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950795288\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1867505620 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8eamzkV8SOaJroNqAGBwmG7MJXSsEoksDtP9bWIf</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867505620\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store"}, "badge": "302 Found"}}