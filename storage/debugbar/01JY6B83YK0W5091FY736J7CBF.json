{"__meta": {"id": "01JY6B83YK0W5091FY736J7CBF", "datetime": "2025-06-20 09:38:20", "utime": **********.243275, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.4.5", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.184443, "end": **********.243288, "duration": 0.05884504318237305, "duration_str": "58.85ms", "measures": [{"label": "Booting", "start": **********.184443, "relative_start": 0, "end": **********.205947, "relative_end": **********.205947, "duration": 0.021503925323486328, "duration_str": "21.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.205963, "relative_start": 0.021519899368286133, "end": **********.24329, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "37.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.208073, "relative_start": 0.*****************, "end": **********.208715, "relative_end": **********.208715, "duration": 0.0006420612335205078, "duration_str": "642μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.213889, "relative_start": 0.029445886611938477, "end": **********.242975, "relative_end": **********.242975, "duration": 0.*****************, "duration_str": "29.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: auth.login", "start": **********.214067, "relative_start": 0.029623985290527344, "end": **********.214067, "relative_end": **********.214067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.auth-session-status", "start": **********.225759, "relative_start": 0.*****************, "end": **********.225759, "relative_end": **********.225759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.231542, "relative_start": 0.04709911346435547, "end": **********.231542, "relative_end": **********.231542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.232698, "relative_start": 0.048254966735839844, "end": **********.232698, "relative_end": **********.232698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.23603, "relative_start": 0.05158710479736328, "end": **********.23603, "relative_end": **********.23603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.237073, "relative_start": 0.052629947662353516, "end": **********.237073, "relative_end": **********.237073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.237249, "relative_start": 0.05280590057373047, "end": **********.237249, "relative_end": **********.237249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.237428, "relative_start": 0.05298495292663574, "end": **********.237428, "relative_end": **********.237428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.primary-button", "start": **********.237745, "relative_start": 0.05330204963684082, "end": **********.237745, "relative_end": **********.237745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.guest", "start": **********.238539, "relative_start": 0.05409598350524902, "end": **********.238539, "relative_end": **********.238539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.application-logo", "start": **********.242281, "relative_start": 0.05783796310424805, "end": **********.242281, "relative_end": **********.242281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 2189688, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": **********.214019, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fauth%2Flogin.blade.php:1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "components.auth-session-status", "param_count": null, "params": [], "start": **********.225668, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/auth-session-status.blade.phpcomponents.auth-session-status", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fauth-session-status.blade.php:1", "ajax": false, "filename": "auth-session-status.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.231531, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.232669, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.236019, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.237065, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.237241, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.23742, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.primary-button", "param_count": null, "params": [], "start": **********.237736, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/primary-button.blade.phpcomponents.primary-button", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fprimary-button.blade.php:1", "ajax": false, "filename": "primary-button.blade.php", "line": "?"}}, {"name": "layouts.guest", "param_count": null, "params": [], "start": **********.23853, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/layouts/guest.blade.phplayouts.guest", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Flayouts%2Fguest.blade.php:1", "ajax": false, "filename": "guest.blade.php", "line": "?"}}, {"name": "components.application-logo", "param_count": null, "params": [], "start": **********.242271, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/application-logo.blade.phpcomponents.application-logo", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fapplication-logo.blade.php:1", "ajax": false, "filename": "application-logo.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "login", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "cache": {"start": **********.184443, "end": **********.243561, "duration": 0.059118032455444336, "duration_str": "59.12ms", "measures": [], "count": 0, "nb_measures": 0}, "session": {"_token": "k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "uri": "GET login", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>", "middleware": "web, guest", "duration": "58.87ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2126614254 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2126614254\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-16996178 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1001 characters\">XSRF-TOKEN=eyJpdiI6IllHVVhiSzFLcmNvWjh3NCt3eFpwQ3c9PSIsInZhbHVlIjoiL2lQNFF0MHRPeFBPbTRQakpXU1h3a0RObHh6azdxdGlHMjJZc3YxdVZQckRNSVJpanh1SGUvOXZQS3ZUMCsrZXBYN3hwcDlWRWY0eE5DZlJwOElmRW9vQnh3OUZjcFFoeTBvZVRQRzZEaytsbXdNcm1WYy90eVl4dW1KdUt2emkiLCJtYWMiOiIwYWE4MmM2MDI3MWYxOWFmMDU0YjI2MDQ0N2MyNzcyNzZmNTc3Mzc0YWU2ZGY2Zjg1YmQzZWRmZDBlNmY3YWU5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InlDSzhzL0F1T01jOUlBd3poNG85cVE9PSIsInZhbHVlIjoiVGV4RG5vOVppZm1RdUtrUkluZ3lQckU4SGNxZUhpc01VKzJibGovZUNhQTNvaUxIcy9Qb2drTUdhZTh1Y1pvOWlyakY4akdzR2VDZHE2LzdtUHBIamp3U1k0dHBqVmk5T0crVnpJbHZTU3FJamxFZUxrWnJFUldUVGlQaGp3R1giLCJtYWMiOiJkZTg1NDIwMDgzZWQyNzBjN2ZhZDczYzRkYTQ0NzI0YTQwZDVmNWJkMDMyNDRhYmUxMTg1MjM0ZWE3NWZmYmY1IiwidGFnIjoiIn0%3D; _ga_Z28525QR15=GS2.1.s1749595141$o5$g0$t1749595141$j60$l0$h0; _ga=GA1.1.E20B8A103D1D49E6A741E19936EFAC67; localauth=localapi9d9bde7eb655a1ef:; _ga_CJB9T8MR54=GS2.1.s1749564387$o1$g1$t1749564392$j55$l0$h0; _ga_WEZVWJKCXR=GS2.1.s1749564388$o1$g0$t1749564388$j60$l0$h0; isNotIncognito=true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16996178\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-577040256 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MPFbKSj1cMFroXoAqBuGEKE7S5ajt4nbmlFoNSOe</span>\"\n  \"<span class=sf-dump-key>_ga_Z28525QR15</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>localauth</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_CJB9T8MR54</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_WEZVWJKCXR</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>isNotIncognito</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577040256\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1591647329 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 09:38:20 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591647329\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2047797489 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047797489\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create"}, "badge": null}}