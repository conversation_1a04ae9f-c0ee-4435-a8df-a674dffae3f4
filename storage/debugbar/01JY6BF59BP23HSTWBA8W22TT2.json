{"__meta": {"id": "01JY6BF59BP23HSTWBA8W22TT2", "datetime": "2025-06-20 09:42:10", "utime": **********.987683, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.968165, "end": **********.987696, "duration": 0.0195310115814209, "duration_str": "19.53ms", "measures": [{"label": "Booting", "start": **********.968165, "relative_start": 0, "end": **********.973275, "relative_end": **********.973275, "duration": 0.005110025405883789, "duration_str": "5.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.973284, "relative_start": 0.0051190853118896484, "end": **********.987697, "relative_end": 9.5367431640625e-07, "duration": 0.014412879943847656, "duration_str": "14.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.974021, "relative_start": 0.005856037139892578, "end": **********.974274, "relative_end": **********.974274, "duration": 0.0002529621124267578, "duration_str": "253μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.977505, "relative_start": 0.009340047836303711, "end": **********.98755, "relative_end": **********.98755, "duration": 0.010045051574707031, "duration_str": "10.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: auth.login", "start": **********.977607, "relative_start": 0.*****************, "end": **********.977607, "relative_end": **********.977607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.auth-session-status", "start": **********.980176, "relative_start": 0.012011051177978516, "end": **********.980176, "relative_end": **********.980176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.982972, "relative_start": 0.014806985855102539, "end": **********.982972, "relative_end": **********.982972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.983283, "relative_start": 0.015118122100830078, "end": **********.983283, "relative_end": **********.983283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.984436, "relative_start": 0.016271114349365234, "end": **********.984436, "relative_end": **********.984436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.984704, "relative_start": 0.01653909683227539, "end": **********.984704, "relative_end": **********.984704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.984788, "relative_start": 0.01662302017211914, "end": **********.984788, "relative_end": **********.984788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.984876, "relative_start": 0.016710996627807617, "end": **********.984876, "relative_end": **********.984876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.primary-button", "start": **********.985065, "relative_start": 0.016900062561035156, "end": **********.985065, "relative_end": **********.985065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.guest", "start": **********.985259, "relative_start": 0.017094135284423828, "end": **********.985259, "relative_end": **********.985259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.application-logo", "start": **********.987343, "relative_start": 0.019178152084350586, "end": **********.987343, "relative_end": **********.987343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 2075344, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 11, "nb_templates": 11, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": **********.977596, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fauth%2Flogin.blade.php:1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "components.auth-session-status", "param_count": null, "params": [], "start": **********.980166, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/auth-session-status.blade.phpcomponents.auth-session-status", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fauth-session-status.blade.php:1", "ajax": false, "filename": "auth-session-status.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.982962, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.983275, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.984428, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.984696, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.984781, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.984869, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.primary-button", "param_count": null, "params": [], "start": **********.985058, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/primary-button.blade.phpcomponents.primary-button", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fprimary-button.blade.php:1", "ajax": false, "filename": "primary-button.blade.php", "line": "?"}}, {"name": "layouts.guest", "param_count": null, "params": [], "start": **********.98525, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/layouts/guest.blade.phplayouts.guest", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Flayouts%2Fguest.blade.php:1", "ajax": false, "filename": "guest.blade.php", "line": "?"}}, {"name": "components.application-logo", "param_count": null, "params": [], "start": **********.987334, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/application-logo.blade.phpcomponents.application-logo", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fapplication-logo.blade.php:1", "ajax": false, "filename": "application-logo.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "login", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "cache": {"start": **********.968165, "end": **********.988175, "duration": 0.020009994506835938, "duration_str": "20.01ms", "measures": [], "count": 0, "nb_measures": 0}, "session": {"_token": "zzVSDeYmFGWpwiasHinRVBC9EBpZMGxQmBVe3QVz", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/dashboard\"\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "uri": "GET login", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php:18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>", "middleware": "web, guest", "duration": "20.13ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1067963323 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1067963323\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1255 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InF3QWQ4N2NBNnJDN3FISlV6V3BPaFE9PSIsInZhbHVlIjoiNmhpUC96NWFnU2t4UENTUWZlakd2VFREaGp0Z2ovTUtnUC8xY2luc0JqUGgrSlhuTHBuUWVRYktTUUt5MGp4TzR6S1c0dDdXL0xYR29lWExnSlRiMlR3TTBzWTBRcXBZUGhNWkY0S0c1NHlieDhmS2paZGhuRVA4MmdkVlFlaExQRjhYVUlLSk5lL0JZeTdUbGI2dkZqUlRMcUswMVVkRm5TUVI0Nlhvck1yWGtQRzRyTGx4allsK0l5U3V2djRUMmJMZ2ZZc2RxN2o2WXdjOFJ0MVF0UW9YN21zN0tybDFZSlEreFd2SFRkbz0iLCJtYWMiOiI2NWI3ZDY1MTg1NGFlOGRiNDY3NzE3MjE1Y2M5M2Y0Y2EyMDcxOTFlNDczZTMwYmVhOGViM2FhOTMwMjJkMGVmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkxTRVdHNlc0MVNxMnJBaExia3gzWnc9PSIsInZhbHVlIjoiRzMxY3NuL1NYcnkyR21LS1lGbmlXb3Bwd1lIUEIwNGZWQmMxQ0QvK0VPTVNoTGF5WUpVVndtUm1LbXdPeENrQ3VkemRVZ1FHNlVyR3Z5dVEveGJvY1grdE52UFpWWmhoMGcwM21ZNkZ4aG5HQUtPM282cS9hMGJsVTdiOHpxb3UiLCJtYWMiOiI2ZTYwMTBjNjZlOGZkM2FhOGRjMTQxNjRiODZkYjBiYmVlNjA5MjgzMWNkNDFmN2JhY2MzMzZmYmQ0YWZlNGUxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkVyQU5nK2JWNVViSkxrLzd3T0RnN0E9PSIsInZhbHVlIjoidkxnR3ozWEdSUlJKTVFhOHBBOE4xSXpVblFsMTNOREJoSzJPdFdWOXZ2dU5hdit4Z2JMNkhHMzJLa01xUUJFbmVvSUF2dFhRQ2JLbS9ZOGx4WDFtR25aNGt3Z3lhTTlCNHArNlN1TE4vZWdFMVZXY3BzRndnZHVBS3ZPbE03RXUiLCJtYWMiOiI1OTJmMmRmMjIzZWU0MjUxYTFlMzVmOTA3ODg2MTVlMWEzMWJlNGRlMTFiNmUwZmE1ZmExMGI0OWQxOTdjYjQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1424623728 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zzVSDeYmFGWpwiasHinRVBC9EBpZMGxQmBVe3QVz</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bf6sPAUN440Zpd2VPpBPC2A8V9S0KUF05WmHPeqm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424623728\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1152693614 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 09:42:10 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152693614\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-160435845 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zzVSDeYmFGWpwiasHinRVBC9EBpZMGxQmBVe3QVz</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-160435845\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create"}, "badge": null}}