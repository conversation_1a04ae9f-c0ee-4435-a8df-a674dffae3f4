{"__meta": {"id": "01JY6B8BWWEN5CNSCGSQ08RNMR", "datetime": "2025-06-20 09:38:28", "utime": **********.380417, "method": "GET", "uri": "/register", "ip": "127.0.0.1"}, "php": {"version": "8.4.5", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.35009, "end": **********.380434, "duration": 0.030344009399414062, "duration_str": "30.34ms", "measures": [{"label": "Booting", "start": **********.35009, "relative_start": 0, "end": **********.360683, "relative_end": **********.360683, "duration": 0.010592937469482422, "duration_str": "10.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.360693, "relative_start": 0.010602951049804688, "end": **********.380435, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "19.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.362664, "relative_start": 0.012573957443237305, "end": **********.363039, "relative_end": **********.363039, "duration": 0.0003750324249267578, "duration_str": "375μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.365171, "relative_start": 0.015080928802490234, "end": **********.380033, "relative_end": **********.380033, "duration": 0.***************, "duration_str": "14.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: auth.register", "start": **********.365385, "relative_start": 0.015295028686523438, "end": **********.365385, "relative_end": **********.365385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.375158, "relative_start": 0.025068044662475586, "end": **********.375158, "relative_end": **********.375158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.37553, "relative_start": 0.025439977645874023, "end": **********.37553, "relative_end": **********.37553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.375881, "relative_start": 0.025790929794311523, "end": **********.375881, "relative_end": **********.375881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.376309, "relative_start": 0.026218891143798828, "end": **********.376309, "relative_end": **********.376309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.376424, "relative_start": 0.026334047317504883, "end": **********.376424, "relative_end": **********.376424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.376706, "relative_start": 0.02661585807800293, "end": **********.376706, "relative_end": **********.376706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.376911, "relative_start": 0.026820898056030273, "end": **********.376911, "relative_end": **********.376911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.377012, "relative_start": 0.026921987533569336, "end": **********.377012, "relative_end": **********.377012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.377129, "relative_start": 0.027039051055908203, "end": **********.377129, "relative_end": **********.377129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": **********.377551, "relative_start": 0.02746105194091797, "end": **********.377551, "relative_end": **********.377551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": **********.377723, "relative_start": 0.027632951736450195, "end": **********.377723, "relative_end": **********.377723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.377951, "relative_start": 0.02786087989807129, "end": **********.377951, "relative_end": **********.377951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.primary-button", "start": **********.378342, "relative_start": 0.02825188636779785, "end": **********.378342, "relative_end": **********.378342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.guest", "start": **********.37871, "relative_start": 0.028620004653930664, "end": **********.37871, "relative_end": **********.37871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.application-logo", "start": **********.379737, "relative_start": 0.029646873474121094, "end": **********.379737, "relative_end": **********.379737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 2596960, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.4.5", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "auth.register", "param_count": null, "params": [], "start": **********.365367, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/auth/register.blade.phpauth.register", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fauth%2Fregister.blade.php:1", "ajax": false, "filename": "register.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.375146, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.37552, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.375871, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.376301, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.376416, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.376691, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.376903, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.377004, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.377121, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": **********.377536, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php:1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": **********.377697, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.377915, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php:1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.primary-button", "param_count": null, "params": [], "start": **********.378314, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/primary-button.blade.phpcomponents.primary-button", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fprimary-button.blade.php:1", "ajax": false, "filename": "primary-button.blade.php", "line": "?"}}, {"name": "layouts.guest", "param_count": null, "params": [], "start": **********.378693, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/layouts/guest.blade.phplayouts.guest", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Flayouts%2Fguest.blade.php:1", "ajax": false, "filename": "guest.blade.php", "line": "?"}}, {"name": "components.application-logo", "param_count": null, "params": [], "start": **********.379706, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/components/application-logo.blade.phpcomponents.application-logo", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fcomponents%2Fapplication-logo.blade.php:1", "ajax": false, "filename": "application-logo.blade.php", "line": "?"}}]}, "route": {"uri": "GET register", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\RegisteredUserController@create<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisteredUserController.php:21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "register", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisteredUserController.php:21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/RegisteredUserController.php:21-24</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "cache": {"start": **********.35009, "end": **********.380816, "duration": 0.030725955963134766, "duration_str": "30.73ms", "measures": [], "count": 0, "nb_measures": 0}, "session": {"_token": "k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/register", "action_name": "register", "controller_action": "App\\Http\\Controllers\\Auth\\RegisteredUserController@create", "uri": "GET register", "controller": "App\\Http\\Controllers\\Auth\\RegisteredUserController@create<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisteredUserController.php:21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisteredUserController.php:21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/RegisteredUserController.php:21-24</a>", "middleware": "web, guest", "duration": "30.66ms", "peak_memory": "4MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1693251610 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1693251610\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1616261946 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1616261946\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1185254034 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1001 characters\">XSRF-TOKEN=eyJpdiI6IlhYenRtVlo5YXlOMUlqdE13aGZ0aXc9PSIsInZhbHVlIjoiRHBwK3I3S2JCRTJ4SWl6UXF5T0F1b3BORGt6WkhNNWRSZVBRSXZPanIwZ0IvdEpGZGlUdi92T1E3Y3lhbUhmNnIzR0wxbmpIVlJaSlY2YnBpYzMwaTlnOER5cFo1anNIbnQxYi96UDVrVFMyd3JGRnU0SHRadlNSSlJ4bVZ0K20iLCJtYWMiOiI1YzA2NTJmYmMyNzM4N2VhODI3YTM3NDg1OTExZmRhNmFhM2M1ZGM5ODI1ZDg4OTA4ZjdmNzAxYWFlNTI1M2MwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ii9pL2lXNk9QNGhtRkdITlQ4MEhWVWc9PSIsInZhbHVlIjoibGNNdEN1bkNvRmdKSDRTMG5kQWEyQ0NNK21ocGhPY2dsU2NzRWZLRVFmYSthRCt5dTFWM2xZWTdNa2drS2VzMEJxTE9zWjk2OVg3bEJWT0c4TVdVdkRkckMycHk2ekZhSWJBQVYyOWNTN0lERng0Vjk3U1dCb1IrUmkzTEVSUW4iLCJtYWMiOiJjZWM4NzVjNWUwYTIzNmZkODUwODg2MTczNjFiN2U3ZjczMmE5MTZlZjA3ZWM4ODFiY2ZhN2ExMDZhODM5NDA0IiwidGFnIjoiIn0%3D; _ga_Z28525QR15=GS2.1.s1749595141$o5$g0$t1749595141$j60$l0$h0; _ga=GA1.1.E20B8A103D1D49E6A741E19936EFAC67; localauth=localapi9d9bde7eb655a1ef:; _ga_CJB9T8MR54=GS2.1.s1749564387$o1$g1$t1749564392$j55$l0$h0; _ga_WEZVWJKCXR=GS2.1.s1749564388$o1$g0$t1749564388$j60$l0$h0; isNotIncognito=true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">fr-FR,fr;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185254034\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1566043618 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MPFbKSj1cMFroXoAqBuGEKE7S5ajt4nbmlFoNSOe</span>\"\n  \"<span class=sf-dump-key>_ga_Z28525QR15</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>localauth</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_CJB9T8MR54</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_WEZVWJKCXR</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>isNotIncognito</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566043618\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1654341765 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 09:38:28 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654341765\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-703708946 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0GOUEL0AHlwm6AWDZcytLYErTTyCaPt8rNrKxDX</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8001/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703708946\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/register", "action_name": "register", "controller_action": "App\\Http\\Controllers\\Auth\\RegisteredUserController@create"}, "badge": null}}