{"__meta": {"id": "01JY6ARNE94NF8TKJWFSXWV8VB", "datetime": "2025-06-20 09:29:53", "utime": **********.866505, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.484581, "end": **********.866523, "duration": 0.3819420337677002, "duration_str": "382ms", "measures": [{"label": "Booting", "start": **********.484581, "relative_start": 0, "end": **********.778513, "relative_end": **********.778513, "duration": 0.****************, "duration_str": "294ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.778536, "relative_start": 0.*****************, "end": **********.866525, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "87.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.830256, "relative_start": 0.***************, "end": **********.837594, "relative_end": **********.837594, "duration": 0.007338047027587891, "duration_str": "7.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.854539, "relative_start": 0.*****************, "end": **********.861025, "relative_end": **********.861025, "duration": 0.006486177444458008, "duration_str": "6.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: welcome", "start": **********.859856, "relative_start": 0.****************, "end": **********.859856, "relative_end": **********.859856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 6014064, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "welcome", "param_count": null, "params": [], "start": **********.85984, "type": "blade", "hash": "blade/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/resources/views/welcome.blade.phpwelcome", "xdebug_link": {"url": "vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Fresources%2Fviews%2Fwelcome.blade.php:1", "ajax": false, "filename": "welcome.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#319\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#316 …}\n  file: \"/Users/<USER>/Desktop/Developper/projets_alex/taxe-app/routes/web.php\"\n  line: \"16 to 18\"\n}", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Froutes%2Fweb.php:16\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:16-18</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "cache": {"start": **********.484581, "end": **********.880686, "duration": 0.****************, "duration_str": "396ms", "measures": [], "count": 0, "nb_measures": 0}, "session": {"_token": "zzVSDeYmFGWpwiasHinRVBC9EBpZMGxQmBVe3QVz"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": null, "controller_action": "Closure", "uri": "GET /", "file": "<a href=\"vscode://file/%2FUsers%2Fmacbook%2FDesktop%2FDevelopper%2Fprojets_alex%2Ftaxe-app%2Froutes%2Fweb.php:16\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:16-18</a>", "middleware": "web", "duration": "393ms", "peak_memory": "10MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1927955711 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1927955711\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1453251768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1453251768\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"540 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InF3QWQ4N2NBNnJDN3FISlV6V3BPaFE9PSIsInZhbHVlIjoiNmhpUC96NWFnU2t4UENTUWZlakd2VFREaGp0Z2ovTUtnUC8xY2luc0JqUGgrSlhuTHBuUWVRYktTUUt5MGp4TzR6S1c0dDdXL0xYR29lWExnSlRiMlR3TTBzWTBRcXBZUGhNWkY0S0c1NHlieDhmS2paZGhuRVA4MmdkVlFlaExQRjhYVUlLSk5lL0JZeTdUbGI2dkZqUlRMcUswMVVkRm5TUVI0Nlhvck1yWGtQRzRyTGx4allsK0l5U3V2djRUMmJMZ2ZZc2RxN2o2WXdjOFJ0MVF0UW9YN21zN0tybDFZSlEreFd2SFRkbz0iLCJtYWMiOiI2NWI3ZDY1MTg1NGFlOGRiNDY3NzE3MjE1Y2M5M2Y0Y2EyMDcxOTFlNDczZTMwYmVhOGViM2FhOTMwMjJkMGVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1496093780 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496093780\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1711082245 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 09:29:53 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711082245\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-208694787 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zzVSDeYmFGWpwiasHinRVBC9EBpZMGxQmBVe3QVz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208694787\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "controller_action": "Closure"}, "badge": null}}